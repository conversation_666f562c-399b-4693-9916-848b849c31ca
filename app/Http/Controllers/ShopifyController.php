<?php

namespace App\Http\Controllers;

use GuzzleHttp\Client;
use App\Services\ShopifyService;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Collection;
use App\Models\Customer;
use App\Models\Order;
use App\Models\OrderItems;
use App\Models\Membership;
use App\Models\BonusEntries;
use App\Imports\ProductImport;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;

use App\Models\User;
use App\Models\UserEnrollment;

use Carbon\Carbon;

use App\Exports\ProductExport;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Facades\Excel;

use App\Mail\SetupPaymentLinkMail;
use Illuminate\Support\Facades\Mail;

use DataTables;

class ShopifyController extends Controller
{
    protected $shopifyService;

    public function __construct(ShopifyService $shopifyService)
    {
        $this->shopifyService = $shopifyService;
    }

    // Test for generate excel
    public function testGenerateExcel()
    {
        $heading_columns = [
            'ID',
            'Name',
            'Email',
            'contact number',
            'Created At',
            'Updated At'
        ];
        $data = [
            [1, 'John Doe', '<EMAIL>', '9879537100', '2021-01-01', '2021-01-01'],
            [2, 'Jane Smith', '<EMAIL>', '9879784797', '2021-02-01', '2021-02-01'],
            // Add more data as needed
        ];
        return Excel::download(new ProductExport($heading_columns, $data), 'products.xlsx');
    }

    public function uploadProductFile(Request $request)
    {
        // Validate the uploaded file
        $request->validate([
            'uploadProduct' => 'required|file|mimes:xlsx,xls|max:2048', // Adjust rules as needed
        ]);

        // Handle the uploaded file
        if ($request->hasFile('uploadProduct')) {
            $file = $request->file('uploadProduct');
            //$fileName = time() . '_' . $file->getClientOriginalName();
            $fileName = "products.xlsx";

            // Store the file in the 'uploads' directory
            $file->storeAs('uploads', $fileName, 'public');

            return back()->with('success', 'File uploaded successfully.')->with('file', $fileName);
        }
        return back()->with('error', 'No file uploaded.');
    }

    public function downloadProductFile()
    {
        $path = storage_path('app/public/uploads/products.xlsx');

        if (file_exists($path)) {
            return response()->download($path);
        }

        return back()->with('error', 'File not found.');
    }

    public function uploadProducts(Request $request)
    {
        // Validate the uploaded file
        $request->validate([
            'uploadProduct' => 'required|file|mimes:xlsx,xls|max:2048',
        ]);

        // Handle the file and import the data
        try {
            Excel::import(new ProductImport, $request->file('uploadProduct'));

            return redirect()->back()->with('success', 'Products imported successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'There was an error importing the file: ' . $e->getMessage());
        }
    }

    public function importProducts()
    {
        $filePath = storage_path('app/public/uploads/products.xlsx');
        if (file_exists($filePath)) {
            $data = Excel::toArray([], $filePath);

            // Process the first sheet (you can loop through sheets if needed)
            $sheetData = $data[0]; // Get the first sheet

            // Prepare row-wise and column-wise data
            $rows = [];
            $columns = [];

            $r = 1; // row
            $p = -1; // product
            $v = 0; // variation
            $shopify_products = [];
            foreach ($sheetData as $rowIndex => $row) {
                if ($row[7] == "") exit; // Exit from checking excel data when no data found
                if ($r > 1) { // skip first row bcz it's for headings
                    $rows[$rowIndex] = $row; // Add row-wise data
                    if ($rows[$rowIndex][0] != "" && $rows[$rowIndex][1] != "") { // double check for seq no as well shopify id
                        $p++;
                        $v = 0;
                        $shopify_products[$p]['shopify_product_id'] = $row[1];
                        $shopify_products[$p]['title'] = $row[2];
                        $shopify_products[$p]['handle'] = $row[3];
                        $shopify_products[$p]['vendor'] = $row[4];
                        $shopify_products[$p]['product_type'] = $row[5];
                        $shopify_products[$p]['tags'] = $row[6];
                        $shopify_products[$p]['change_status'] = $row[16];
                    } else {
                        $v++;
                    }
                    $shopify_products[$p]['variations'][$v]['variant_id'] = $row[7];
                    $shopify_products[$p]['variations'][$v]['sku'] = $row[8];
                    $shopify_products[$p]['variations'][$v]['barcode'] = $row[9];
                    $shopify_products[$p]['variations'][$v]['variant'] = $row[10];
                    $shopify_products[$p]['variations'][$v]['weight'] = $row[11];
                    $shopify_products[$p]['variations'][$v]['price'] = $row[12];
                    $shopify_products[$p]['variations'][$v]['compare_at_price'] = $row[13];
                    $shopify_products[$p]['variations'][$v]['inventory_item_id'] = $row[14];
                    $shopify_products[$p]['variations'][$v]['inventory_quantity'] = $row[15];
                }
                $r++;
            }

            //$n=0;
            foreach ($shopify_products as $shopify_product) {
                //if($n<100){
                if ($shopify_product['change_status'] == "Y") {
                    //echo "<pre>"; print_r($shopify_product); echo "</pre>"; continue;
                    $this->shopifyService->importShopifyProduct($shopify_product);
                }
                //}
                //$n++;
            }
            // Return both row-wise and column-wise data
            // return response()->json([
            //     'row_wise' => $rows,
            //     'column_wise' => $columns,
            // ]);
            return redirect()->route('product.list')->with('success', 'Product updated successfully');
        } else {
            return redirect()->route('product.list')->with('fail', 'file not exist...');
        }
    }

    public function exportProducts()
    {
        $new_products = Product::select('id', 'shopify_product_id', 'title', 'handle', 'vendor', 'product_type', 'tags', 'collections', 'sales_channels', 'shopify_variants')
            //->where('shopify_status','Fetched - New Product')
            //->orWhere('shopify_status','New product')
            ->orderBy('id', 'asc')->get();

        if (count($new_products) > 0) {
            $n = 1;
            $excelData = [];
            $heading_columns = ['#', 'shopify_product_id', 'title', 'handle', 'vendor', 'product_type', 'tags', 'variant_id', 'sku', 'barcode', 'variant', 'weight', 'price', 'compare_at_price', 'inventory_item_id', 'inventory_quantity', 'changed?(Y/N)'];
            $n = 1;
            foreach ($new_products as $new_product) {
                $shopify_variants = json_decode($new_product['shopify_variants'], true);
                // $collections = json_decode($new_product['collections'],true);
                // $sales_channels = json_decode($new_product['sales_channels'],true);
                $c = 1;
                foreach ($shopify_variants as $shopify_variant) {
                    if (isset($shopify_variant['variant_id'])) {
                        // $no = $c==1 ? $n : ""; 
                        if ($c == 1) {
                            $formattedData[] = [
                                $n,
                                $new_product['shopify_product_id'],
                                $new_product['title'],
                                $new_product['handle'],
                                $new_product['vendor'],
                                $new_product['product_type'],
                                $new_product['tags'],
                                $shopify_variant['variant_id'],
                                $shopify_variant['sku'],
                                $shopify_variant['barcode'],
                                $shopify_variant['option1'],
                                $shopify_variant['weight'],
                                $shopify_variant['price'],
                                $shopify_variant['compare_at_price'],
                                $shopify_variant['inventory_item_id'],
                                $shopify_variant['inventory_quantity'],
                                'N'
                            ];
                        } else {
                            $formattedData[] = [
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                $shopify_variant['variant_id'],
                                $shopify_variant['sku'],
                                $shopify_variant['barcode'],
                                $shopify_variant['option1'],
                                $shopify_variant['weight'],
                                $shopify_variant['price'],
                                $shopify_variant['compare_at_price'],
                                $shopify_variant['inventory_item_id'],
                                $shopify_variant['inventory_quantity'],
                                ''
                            ];
                        }
                    }
                    $c++;
                }
                $n++;
            }
        }
        // exit;
        $tempPath = storage_path('app/temp/products.xlsx');
        //Excel::store(new ProductExport($heading_columns, $formattedData), 'temp/products.xlsx', 'local'); // store
        return Excel::download(new ProductExport($heading_columns, $formattedData), 'products.xlsx');
    }

    public function countProducts()
    {
        $total_products = $this->shopifyService->countTotalProducts();
        return $total_products;
        //return view('shopify.index', compact('total_products'));
    }

    public function getProducts()
    {
        $products = $this->shopifyService->getProducts();
        return view('shopify.products', compact('products'));
    }

    public function getProductsByLimit($limit, $productId)
    {
        $products = $this->shopifyService->getProductsByLimit($limit, $productId);
        return view('shopify.products', compact('products'));
    }

    public function getProductsIds()
    {
        $products = $this->shopifyService->getProductsIds();
        return view('shopify.products', compact('products'));
    }

    public function productDetail($productId)
    {
        $product = $this->shopifyService->productDetail($productId);
        echo "<pre>";
        print_r($product);
        exit;
        //return view('shopify.product', compact('product'));
    }

    public function getProductDetail($productId)
    {
        $product = Product::where('shopify_product_id', $productId)->get()->toArray();
        // return "testing...";
        // echo "<pre>"; print_r($product); echo "</pre>";

        if ($product) {
            return response()->json(['success' => true, 'product' => $product]);
        }

        return response()->json(['success' => false, 'message' => 'Product not found!']);
    }

    public function storeProducts($limit) // save shopify products to DB
    {
        $products = $this->shopifyService->storeProducts($limit);
        return view('shopify.products', compact('products'));
    }

    public function updateProducts() // save shopify products to DB
    {
        $products = $this->shopifyService->updateProducts();
    }

    public function updateProduct(Request $request, $id)
    {
        $product = Product::find($id);
        $input = [
            'shopify_product_id' => $product->shopify_product_id,
            'new_title' => $request->title,
            'new_tags' => $request->tagName,
            'vendor' => $request->vendorName,
            'product_type' => $request->productType,
            'new_collections' => $request->collectionName,
            'current_sales_ids' => $request->current_sales_ids,
            'sales_channels' => $request->salesChannels,
            'descriptionHtml' => $request->description,
            'variant_keys' => $request->variant_key,
            'variant_values' => $request->variant_values,
        ];
        // echo "<pre>"; print_r($input); echo "</pre>"; exit;
        $products = $this->shopifyService->updateShopifyProduct($input);
        return redirect()->route('product.list')->with('success', 'Product updated successfully');
    }

    public function editGiveaway(Request $request)
    {
        $id = $request->id;
        $giveawayDet = DB::table('giveaways')->select('id', 'giveaway_id', 'title', 'prize_amount', 'image', 'draw_on', 'winner')->where('id', $id)->get()->first();
        $giveaway = json_decode(json_encode($giveawayDet), true);
        //echo "<pre>"; print_r($giveaway); echo "</pre>"; exit;
        return view('adminpanel.giveaway.edit', compact('giveaway'));
    }

    public function getDrawDetail(Request $request)
    {
        $giveawayDet = DB::table('giveaways')->select('id', 'giveaway_id', 'title', 'prize_amount', 'image', 'draw_on', 'winner')->get()->first();
        $giveaway = json_decode(json_encode($giveawayDet), true);

        $drawTime = $giveaway['draw_on'];
        $dateTime = new \DateTime($drawTime);
        $isoFormat = $dateTime->format("Y-m-d\TH:i:s");

        return response()->json([
            'code' => 'Success',
            'message' => $isoFormat,
        ]);
    }

    public function checkPaymentMethod(Request $request)
    {
        $customer_id = $request->customer_id;
        Log::info('Customer ID : ' . $customer_id);
        $paymentMethodDetail = Customer::where('shopify_customer_id', $customer_id)
            ->get()
            ->toArray();

        Log::info("Payment Method Detail : ");
        Log::info($paymentMethodDetail);

        $paymentMethodArray = $paymentMethodDetail ? $paymentMethodDetail : [];

        // $paymentMethodUrl = $paymentMethodArray[0]['payment_method_url'];

        if (isset($paymentMethodArray[0]) && empty($paymentMethodArray[0]['payment_method_id'])) {
            Log::info($paymentMethodArray[0]['payment_method_id'] . " : " .  $paymentMethodArray[0]['payment_method_url']);

            if ($paymentMethodArray[0]['payment_method_url'] != '') {
                Log::info('Checking payment menthod id.');

                $response = $this->shopifyService->getPaymentMethod($paymentMethodArray[0]['shopify_customer_id'], $paymentMethodArray[0]['stripe_customer_id'], $paymentMethodArray[0]['email']);

                if ($response == "Payment method received") {
                    return response()->json([
                        'code' => "Success",
                        'message' => "",
                    ]);
                } elseif ($response == "Payment method not created") {
                    return response()->json([
                        'code' => "Fail",
                        'message' => $paymentMethodArray[0]['payment_method_url'],
                    ]);
                }
            }
        } else {
            return response()->json([
                'code' => "Success",
                'message' => "",
            ]);
        }
        // return response()->json(['code' => 0, 'message' => $isoFormat]);
    }
    public function updateCardDetails(Request $request)
    {
        try {
            $customer_id = $request->customer_id;

            Log::info("Update card details request for customer: " . $customer_id);

            // Get customer details from database
            $customerData = Customer::where('shopify_customer_id', $customer_id)->first();

            if (!$customerData) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Customer not found'
                ]);
            }

            // Generate update URL using Stripe
            $updateUrl = $this->shopifyService->createCardUpdateSession(
                $customerData->stripe_customer_id,
                $customerData->email
            );

            if ($updateUrl) {
                return response()->json([
                    'status' => 'success',
                    'update_url' => $updateUrl,
                    'message' => 'Update link generated successfully'
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to generate update link'
                ]);
            }
        } catch (\Exception $e) {
            Log::error("Error in updateCardDetails: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while generating the update link'
            ]);
        }
    }
    public function updateGiveaway(Request $request)
    {
        $response = $this->shopifyService->updateGiveaway($request);
        // echo "testing..."; exit;
        return redirect()->route('giveaway.list');
    }

    public function cronUpdateMembership()
    {
        $this->shopifyService->cronUpdateMembership();
    }

    public function cronUpdateRewardPoints()
    {
        $this->shopifyService->cronUpdateRewardPoints();
    }

    public function cronUpdateBonusPoints()
    {
        $this->shopifyService->cronUpdateBonusPoints();
    }

    public function getBonusPoints(Request $request)
    {
        // Log::info("Customer ID : " . $request->customer_id); exit;
        $response = $this->shopifyService->getBonusPoints($request->customer_id);
        // Log::info($response);
        return $response;
    }

    public function applyBonusPoints(Request $request)
    {
        Log::info("Customer ID : " . $request->customer_id);
        Log::info("Bonus Points : " . $request->bonus_points);
        $response = $this->shopifyService->applyBonusPoints($request);
        Log::info($response);
        return $response;
    }

    public function continueMembershipOffer(Request $request)
    {
        Log::info("Customer ID : " . $request->customer_id);
        Log::info("Continue with membership offer (50%)");
        $response = $this->shopifyService->continueMembershipOffer($request);
        // Log::info($response);
        return $response;
    }

    public function cancelMembership(Request $request)
    {
        Log::info("Customer ID : " . $request->customer_id);
        Log::info("Cancel membership process...");
        $response = $this->shopifyService->cancelMembership($request);
        return $response;
        // Log::info($response);
        // return $response;
    }

    public function tagsAjax(Request $request)
    {
        return $request;
        $data = [];
        if ($request->has('q')) {
            $search = $request->q;
            $product = Product::select("id", "title")
                ->where('title', 'LIKE', "%$search%")
                ->get();
        }
        return response()->json($data);
    }

    public function listDiscounts()
    { // in use
        $discounts = DB::table('discounts')->select('id', 'discount_id', 'title', 'discount_type', 'discount_amount', 'target_type', 'usage_limit', 'starts_at', 'ends_at')->orderBy('id', 'asc')->get()->toArray();

        // echo "<pre>"; print_r($discounts); echo "</pre>"; exit;

        $data = $discounts;
        $n = 1;
        return Datatables::of($data)
            ->addIndexColumn()
            ->addColumn('discount_id', function ($row) {
                return $row->discount_id;
            })
            ->addColumn('title', function ($row) {
                return $row->title;
            })
            ->addColumn('discount_type', function ($row) {
                return $row->discount_type;
            })
            ->addColumn('discount_amount', function ($row) {
                return $row->discount_amount;
            })
            ->addColumn('target_type', function ($row) {
                return $row->target_type;
            })
            ->addColumn('usage_limit', function ($row) {
                return $row->usage_limit;
            })
            ->addColumn('starts_at', function ($row) {
                return $row->starts_at;
            })
            ->addColumn('ends_at', function ($row) {
                return $row->ends_at;
            })
            ->addColumn('action', function ($row) {
                $btn = '';
                $btn .= '<a href="discounts/edit/' . $row->id . '" class="btn btn-warning btn-xs mr-2"><i class="fa fa-pencil-alt"></i></a>';
                return $btn;
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    public function listGiveaways()
    { // in use
        $giveaways = DB::table('giveaways')->select('id', 'giveaway_id', 'title', 'prize_amount', 'image', 'draw_on', 'winner')->orderBy('id', 'asc')->get()->toArray();

        $data = $giveaways;

        // echo "<pre>"; print_r($giveaways); echo "</pre>"; exit;

        $n = 1;
        return Datatables::of($data)
            ->addIndexColumn()
            ->addColumn('giveaway_id', function ($row) {
                return $row->giveaway_id;
            })
            ->addColumn('title', function ($row) {
                return $row->title;
            })
            ->addColumn('prize_amount', function ($row) {
                return $row->prize_amount;
            })
            ->addColumn('draw_on', function ($row) {
                return $row->draw_on;
            })
            ->addColumn('winner', function ($row) {
                return $row->winner;
            })
            ->addColumn('action', function ($row) {
                $btn = '';
                $btn .= '<a href="giveaways/edit/' . $row->id . '" class="btn btn-warning btn-xs mr-2"><i class="fa fa-pencil-alt"></i></a>';
                return $btn;
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    public function showOrders(Request $request)
    {
        $orders = DB::table('shopify_orders')->orderBy('order_time', 'asc')->get();
        // echo "<pre>"; print_r($orders); echo "</pre>"; exit;
        return view('adminpanel.shopify_orders.view', compact('orders'));
    }

    public function listShopifyOrders()
    { // in use
        $orders = DB::table('shopify_orders')->select('id', 'shopify_order_id', 'order_detail', 'order_time')->orderBy('id', 'asc')->get();
        $data = $orders;
        $n = 1;
        return Datatables::of($data)
            ->addIndexColumn()
            ->addColumn('shopify_order_id', function ($row) {
                return $row->shopify_order_id;
            })
            ->addColumn('contact_detail', function ($row) {
                $orderItems = json_decode($row->order_detail, true);
                return $orderItems['contact_email'];
            })
            ->addColumn('order_total', function ($row) {
                $orderItems = json_decode($row->order_detail, true);
                return $orderItems['currency'] . " " . $orderItems['total_price'];
            })
            ->addColumn('order_time', function ($row) {
                return $row->order_time;
            })
            ->addColumn('action', function ($row) {
                $btn = '';
                $btn .= '<a href="#" class="btn btn-warning btn-xs mr-2"><i class="fa fa-pencil-alt"></i></a>';
                $btn .= '<button class="my-custom-button btn btn-sm btn-outline-primary" data-id="' . $row->shopify_order_id . '">view</button>';
                return $btn;
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    public function getOrderDetailPopup(Request $request)
    {
        $orderId = $request->id;
        $order = DB::table('shopify_orders')
            ->select('id', 'shopify_order_id', 'order_time', 'order_detail')
            ->where('shopify_order_id', $orderId)
            ->get()
            ->toArray();
        if ($order) {
            //return($orderId);
            return ($order);
        } else {
            return ("No detail found");
        }
        //return($orderId);
    }

    public function edit($productId)
    {
        $product = $this->shopifyService->getProduct($productId);
        //echo "<pre>"; print_r($product); exit;
        return view('shopify.edit', compact('product'));
    }

    public function updateTags(Request $request, $productId)
    {
        $tags = $request->input('tags');
        $this->shopifyService->updateProductTags($productId, $tags);
        return redirect()->route('shopify.index')->with('success', 'Product tags updated successfully.');
    }


    public function show(Request $request)
    {
        // if ($request->ajax()) {
        // }
        $products = Product::orderBy('title', 'asc')->get();
        return view('adminpanel.product.view', compact('products'));
    }

    // update variants which are mismatched 
    public function updateDescription()
    {
        $products = $this->shopifyService->updateDescription();
    }

    public function getAllSalesChannes()
    {
        $products = $this->shopifyService->getAllSalesChannes();
    }

    public function getAllDiscounts()
    {
        $this->shopifyService->getAllDiscounts();
    }

    public function getCustomers($limit)
    {
        $products = $this->shopifyService->getCustomers($limit);
        return view('shopify.customers', compact('customers'));
    }

    public function editCustomer(Request $request)
    {
        $id = $request->id;
        $customerDet = DB::table('customers')->select('id', 'shopify_customer_id', 'first_name', 'last_name', 'tags')->where('id', $id)->get()->first();
        $customer = json_decode(json_encode($customerDet), true);
        // echo "<pre>"; print_r($customer); echo "</pre>"; exit;

        // Tags
        $tags = [];
        if ($customer['tags'] != '') {
            $tags = array_map('trim', explode(',', $customer['tags']));
        }

        $allTags = DB::table('discounts')
            ->get() // Correct way to retrieve records
            ->pluck('title')
            ->flatMap(function ($tags) {
                return explode(',', $tags);
            })
            ->map(fn($tag) => trim($tag))
            ->unique()
            ->values()
            ->toArray();
        sort($allTags);

        return view('adminpanel.customer.edit', compact('customer', 'allTags', 'tags'));
    }

    public function updateCustomer(Request $request, $id)
    {
        $discount_tags = implode(",", $request->tagName);

        $customerDet = DB::table('customers')->select('id', 'shopify_customer_id')->where('id', $id)->get()->first();
        $customerId = json_decode(json_encode($customerDet), true)['shopify_customer_id'];

        $discountDet = DB::table('discounts')->select('id', 'discount_id', 'title')->where('title', $request->tagName[0])->get()->first();
        $discountId = json_decode(json_encode($discountDet), true)['discount_id'];

        // echo $customerId . " : " . $discountId; exit;

        // update to shopify store
        $this->shopifyService->updateDiscountForCustomers($discountId, $customerId, $discount_tags);

        // update to DB
        DB::table('customers')->where('id', $id)->update([
            'tags' => $discount_tags
        ]);

        return redirect()->route('customer.list')->with('success', 'Customer detail updated successfully');
    }

    function updateCustomerTags($customerId, $tags)
    {
        $query = <<<'GRAPHQL'
        query GetCustomerTags($id: ID!) {
            customer(id: $id) {
                id
                tags
            }
        }
        GRAPHQL;

        $variables = [
            'id' => "gid://shopify/Customer/$customerId"
        ];

        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $accessToken,
            'Content-Type' => 'application/json',
        ])->post("https://$shopifyStore/admin/api/2024-01/graphql.json", [
            'query' => $query,
            'variables' => $variables,
        ]);
    }


    public function getProductsSalesChannels($productId)
    {
        // Prepare an array to store product sales channels
        $productSalesChannels = [];
        $publications = $this->shopifyService->getProductSalesChannles($productId);
        // echo "<pre>"; print_r($publications); exit;
        //('GET', "/admin/api/2023-01/products/{$productId}/publications.json")->body->publications;

        $channels = [];
        for ($c = 0; $c < count($publications); $c++) {
            $channels[$c]['channel'] = $publications[$c]['publication']['name'];
            $channels[$c]['id'] = $this->extractIdFromGid($publications[$c]['publication']['id']);
        }
        $channels_detail = json_encode($channels);
        return response()->json($productSalesChannels);
    }

    public function createCollections()
    {
        $products = $this->shopifyService->createCollections();
        //return view('shopify.products', compact('products'));
    }

    public function addCustomCollectionsToProducts()
    {
        $response = $this->shopifyService->addCustomCollectionsToProducts();
    }

    public function uploadImagesToProducts()
    {
        $products = $this->shopifyService->uploadImagesToProducts();
    }

    public function updateInventoryTracking()
    {
        $products = $this->shopifyService->updateInventoryTracking();
        //return view('shopify.products', compact('products'));
    }

    public function updateShopifyInventory()
    { // check
        $products = $this->shopifyService->updateShopifyInventory();
        //return view('shopify.products', compact('products'));
    }

    public function addProductToShopify()
    {
        $products = $this->shopifyService->addProductToShopify();
        //return view('shopify.products', compact('products'));
    }

    public function removeShopifyInventory($limit)
    {
        $products = $this->shopifyService->removeShopifyInventory($limit);
        //return view('shopify.products', compact('products'));
    }

    public function generateExcel($headings, $data)
    {
        $heading_columns = $headings;
        $data = $data;
        return Excel::download(new ProductExport($heading_columns, $data), 'products.xlsx');
    }

    public function getShopifyOrdersWebhook(Request $request)
    {
        $response = $this->shopifyService->getShopifyOrdersWebhook($request);
        return $response;
        //return view('shopify.products', compact('products'));
    }

    public function createDraftOrder(Request $request)
    {
        $response = $this->shopifyService->createDraftOrder($request);
        return $response;
    }

    public function getShopifyOrderDetail(Request $request)
    {
        Log::info('Order detail from shopify webhook : ');
        Log::info($request);
        echo "testing convert to array...";

        $shopify_order_id = $order_detail['id'];
        $customer_id =  $order_detail['customer']['id'];
    }

    public function getShopifyOrderStatus($orderId)
    {
        $products = $this->shopifyService->getShopifyOrderStatus($orderId);
    }

    public function getShopifyOrderTracking($orderId)
    {
        $products = $this->shopifyService->getShopifyOrderTracking($orderId);
    }

    public function cancelOrder($orderId)
    {
        $response = $this->shopifyService->cancelOrder($orderId);
        return $response;
    }

    public function cancelRefundOrder($orderId)
    {
        $products = $this->shopifyService->cancelRefundOrder($orderId);
    }

    public function calculateRefundOrder($orderId)
    {
        $products = $this->shopifyService->calculateRefundOrder($orderId);
    }

    public function getAllReviews()
    {
        $products = $this->shopifyService->getAllReviews();
    }

    public function getProductReviews($productId)
    {
        $products = $this->shopifyService->getProductReviews($productId);
        //return view('shopify.products', compact('products'));
    }

    // fetch vendor wise products with REST API
    public function fetchProductsByVendor($vendor)
    {
        $products = $this->shopifyService->fetchProductsByVendor($vendor);
    }

    // Fetch vendor wise products with GraphQL
    public function getFilteredProducts(Request $request)
    {
        $products = $this->shopifyService->getFilteredProducts($request);
    }

    public function showCustomers(Request $request)
    {
        return view('adminpanel.customer.view');
    }

    public function listAllCustomers()
    { // in use
        if (isset($_GET['customer_status']) && $_GET['customer_status'] != '') {
            $customer_status = $_GET['customer_status'];
        } else {
            $customer_status = '';
        }
        // echo $product_status; exit;
        // Compare New Products which are not in old backup
        if ($customer_status != "") {
            $customers = Customer::select('id', 'shopify_customer_id', 'registered_from', 'first_name', 'last_name', 'email', 'created_at')
                ->where('registered_from', $customer_status)
                ->orderBy('id', 'asc')
                ->get();
        } else {
            $customers = Customer::select('id', 'shopify_customer_id', 'registered_from', 'first_name', 'last_name', 'email', 'created_at')
                ->orderBy('id', 'asc')->get();
            // echo "<pre>"; print_r($products); echo "</pre>";
        }
        // echo "<pre>"; print_r($customers); exit;
        return view('customers.customerslist', compact('customers'));
    }

    public function getAllCustomers()
    { // in use
        $customers = Customer::select('id', 'shopify_customer_id', 'first_name', 'last_name', 'email', 'registered_from', 'created_at')
            ->orderBy('id', 'asc')->get();

        $data = $customers;
        $n = 1;
        return Datatables::of($data)
            ->addIndexColumn()
            ->addColumn('shopify_customer_id', function ($row) {
                return $row->shopify_customer_id;
            })
            ->addColumn('first_name', function ($row) {
                return $row->first_name . " " . $row->last_name;
            })
            ->addColumn('email', function ($row) {
                return $row->email;
            })
            ->addColumn('registered_from', function ($row) {
                return $row->registered_from;
            })
            ->addColumn('action', function ($row) {
                $btn = '';
                $btn .= '<a href="customers/edit/' . $row->id . '" class="btn btn-warning btn-xs mr-2"><i class="fa fa-pencil-alt"></i></a>';
                return $btn;
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    public function getShopifyCustomers()
    {
        $this->shopifyService->getShopifyCustomers();
    }

    function getCustomerMetafields()
    {
        $customerId = '';
        $this->shopifyService->getCustomerMetafields($customerId);
    }

    function getCustomerSubscriptions()
    {
        // need to apply code to get customer subscription
    }

    public function createCustomer()
    {
        $this->shopifyService->createCustomer();
    }

    public function shopifyCustomerSignup(Request $request)
    { // graphql
        $response = $this->shopifyService->signupShopifyCustomer($request);
        Log::info($response);
        return ($response);
    }

    public function signupMembership(Request $request)
    {
        $response = $this->shopifyService->signupShopifyCustomer($request);
        return (['success' => true, 'message' => 'Customer created successfully!']);
    }

    public function signupWithOTP(Request $request)
    {
        Log::info("Customer registration with OTP : ");
        Log::info("Email : " . $request->input('email'));
        $response = $this->shopifyService->signupShopifyCustomerByOTP($request);
        return (['success' => true, 'message' => 'Customer created successfully!']);
    }

    public function shopifyCustomerRegister(Request $request)
    {
        Log::info("Customer registered details : ");
        $response = $this->shopifyService->createShopifyCustomer($request);
        Log::info($response);
        return ($response);
    }

    public function shopifyCustomerActivate(Request $request)
    {
        Log::info("Customer updated details : ");
        Log::info($request);
        $response = $this->shopifyService->createShopifyCustomer($request);
    }

    public function setCustomerPassword()
    {
        $this->shopifyService->setCustomerPassword();
    }

    public function createCustomerAccessToken(Request $request)
    {
        $response = $this->shopifyService->createCustomerAccessToken($request);
        return ($response);
    }

    public function testScript()
    {
        $orders = DB::table('shopify_orders')->select('id', 'shopify_order_id', 'order_detail', 'order_time')->orderBy('id', 'asc')->get();
        $data = $orders;
        $ordersArray = json_decode($data, true);
        // echo "<pre>"; print_r($orderDet); echo "</pre>";
        $orderDet = json_decode($ordersArray[0]['order_detail'], true);
        echo "<pre>";
        print_r($orderDet);
        echo "</pre>";
        exit;
    }

    public function getMembershipDetail(Request $request)
    {
        $shopifyCustomerId = $request->customer_id;
        $membershipDetails = Membership::where('shopify_customer_id', $shopifyCustomerId)->orderBy('membership_from', 'asc')->get()->toArray();

        Log::info("Membership Details : ");
        Log::info($membershipDetails);

        $lastMembership = end($membershipDetails);
        Log::info($lastMembership);
        $membershipStatus = $lastMembership['membership_status'] ?? null;
        Log::info("Last Membership Status : " . $membershipStatus);

        // For upcoming membership display purpose to shopify side 
        if ($membershipStatus === 'Active') {
            $lastToDate = Carbon::parse($lastMembership['membership_to']);
            $nextFromDateTime = $lastToDate->copy();
            $nextFromDate = $nextFromDateTime->format('Y-m-d');
            $nextToDateTime = $nextFromDateTime->copy()->addMonth();
            $nextToDate = $nextToDateTime->format('Y-m-d');

            $upcomingMembership = [
                'shopify_customer_id' => $shopifyCustomerId,
                'plan_name' => $lastMembership['plan_name'],
                'membership_from' => $nextFromDate,
                'membership_to' => $nextToDate,
                'applicable_price' => $lastMembership['applicable_price'],
                'applicable_discount' => "-",
                'applicable_entries' => $lastMembership['applicable_entries'],
                'applicable_acm_entries' => $lastMembership['applicable_acm_entries'],
                'membership_status' => 'Recurring'
            ];
            $membershipDetails[] = $upcomingMembership;
        }

        $response = $membershipDetails;
        return $response;
    }

    // This is temporary function to check direct effect of membership plans
    public function updateMembershipDates(Request $request)
    {
        $shopifyCustomerId = $request->customer_id;

        $mebershipDetails = Membership::where('shopify_customer_id', $shopifyCustomerId)->orderBy('membership_from', 'desc')->get()->toarray();
        $lastFromDate = Carbon::parse($mebershipDetails[0]['membership_from']);
        $lastToDate = Carbon::parse($mebershipDetails[0]['membership_to']);

        if ($mebershipDetails[0]['membership_status'] == "Upcoming") {
            // echo "<pre>"; print_r($mebershipDetails); echo "</pre>"; exit;
            $update_response = DB::table('membership_detail')
                ->where('shopify_customer_id', $shopifyCustomerId)
                ->update([
                    'membership_from' => DB::raw('DATE_SUB(membership_from, INTERVAL 1 MONTH)'),
                    'membership_to' => DB::raw('DATE_SUB(membership_to, INTERVAL 1 MONTH)'),
                ]);

            if ($update_response > 0) {
                // Call additional functions only after successful update
                $this->shopifyService->cronUpdateMembership();
                $this->shopifyService->cronUpdateBonusPoints();
                Log::info('Membership dates updated successfully');
                return response()->json(['success' => true, 'message' => 'Membership dates updated successfully']);
            } else {
                Log::info('No membership rows were updated');
                return response()->json(['success' => false, 'message' => 'No membership rows were updated']);
            }
        } else {
            Log::info('No future membership plan to update');
            $response = response()->json(['success' => false, 'message' => 'No future membership plan to update']);
        }
        return $response;
    }

    // ============= Start - Stripe Functions ==============
    public function findCustomerByEmail()
    {
        $response = $this->shopifyService->findCustomerByEmail();
        return $response;
    }

    public function createCustomerByEmail()
    {
        $response = $this->shopifyService->createCustomerByEmail();
        return $response;
    }

    public function getPaymentMethods()
    {
        $response = $this->shopifyService->getPaymentMethods();
        return $response;
    }

    public function makePayment()
    {
        $response = $this->shopifyService->makePayment();
        return $response;
    }

    public function stripeSuccess(Request $request)
    {
        Log::info("Success response from stripe");
        Log::info($request);
        return "Success";
    }

    public function stripeCancel()
    {
        Log::info("Fail response from stripe");
        Log::info($request);
        return "Fail";
    }
    // ============= End - Stripe Functions ==============

    public function testMail()
    {
        $email = '<EMAIL>';
        $url = 'https://noblerewards.wedowebaps.in';
        Mail::to($email)->send(new SetupPaymentLinkMail($url));
        Log::info("Setup link emailed to customer: " . $email);
    }
}
