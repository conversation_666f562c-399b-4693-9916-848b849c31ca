<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        'shopify-customer-register',
        'shopify-customer-activate',
        'api/customer-register-shopify-app',
        'api/shopify-login',
        'signup-membership',
        'get-filtered-products',
        'create-customer-access-token',
        'product/autocomplete-tags',
        'product/update',
        'fetch-draw-detail',
        'create-shopifycart-webhook',
        'api/create-draft-order',
        'apply-bonus-detail',
        'get-bonus-detail',
        'membership/offer',
        'membership/cancel',
        'get-membership-detail',
        'update-membership-dates',
        'check-payment-method',
        'update-card-details'
    ];
}
