<?php

namespace App\Services;

use GuzzleHttp\Client;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Collection;
use App\Models\Order;
use App\Models\OrderItems;
use App\Models\Membership;
use App\Models\BonusEntries;

use Stripe\Stripe;
use Stripe\Customer as StripeCustomer;
use Stripe\Checkout\Session;
use Stripe\PaymentMethod;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\JsonResponse;

use App\Mail\SetupPaymentLinkMail;
use Illuminate\Support\Facades\Mail;

use Carbon\Carbon;

class ShopifyService
{
    protected $client;
    protected $apiKey;
    protected $password;
    protected $storeName;
    protected $apiVersion;
    protected $accessToken;
    protected $accessTokenFront;

    public function __construct()
    {
        $this->apiKey = env('SHOPIFY_API_KEY');
        $this->password = env('SHOPIFY_API_PASSWORD');
        $this->storeName = env('SHOPIFY_STORE_NAME');
        $this->apiVersion = env('SHOPIFY_API_VERSION');
        $this->accessToken = env('SHOPIFY_API_PASSWORD');
        $this->accessTokenFront = env('SHOPIFY_STOREFRONT_API_TOKEN');

        $this->client = new Client([
            'base_uri' => "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/",
            'auth' => [$this->apiKey, $this->password],
        ]);
    }

    public function getShopifyCustomers()
    {
        $limit = 250;
        $pageInfo = null;

        $endpoint = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers.json?limit={$limit}";

        if ($pageInfo) {
            $endpoint .= "&page_info={$pageInfo}";
        }

        $responseCustomer = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->accessToken,
        ])->get($endpoint);

        $responseData = json_decode($responseCustomer->getBody()->getContents(), true);

        // echo "<pre>"; print_r($responseData); echo "</pre>"; exit;

        foreach ($responseData['customers'] as $customer) {
            Customer::updateOrCreate(
                ['shopify_customer_id' => $customer['id']],
                [
                    'registered_from' => $customer['tags'] != '' ? $customer['tags'] : "Shopify",
                    'first_name' => $customer['first_name'],
                    'last_name' => $customer['last_name'],
                    'email' => $customer['email'],
                    'password' => "",
                ]
            );
        }
        return $response->json();
    }

    public function createCustomer()
    {
        $response = $this->client->post('customers.json', [
            'json' => [
                'customer' => [
                    "first_name" => "Manish",
                    "last_name" => "D",
                    "email" => "<EMAIL>",
                    "send_email_invite" => false, // Not required but can be added
                    "send_welcome_email" => false // Not required for creation without password
                ]
            ]
        ]);
    }

    public function signupShopifyCustomer(Request $request)
    {
        $email = $request->input('email');
        $password = $request->input('password');
        $firstName = $request->input('first_name');
        $lastName = $request->input('last_name');
        $phone = $request->input('phone');

        $sendEmailInvitation = false;
        $sendWelcomeEmail = false;
        $verified_email = true;

        try {
            // With Front API
            $response = Http::withHeaders([
                'X-Shopify-Storefront-Access-Token' => $this->accessTokenFront,
                'Content-Type' => 'application/json',
            ])->post("https://{$this->storeName}.myshopify.com/api/{$this->apiVersion}/graphql.json", [
                'query' => '
                        mutation customerCreate($email: String!, $password: String!, $firstName: String!, $lastName: String! ) {
                            customerCreate(input: { email: $email, password: $password, firstName: $firstName, lastName: $lastName }) {
                                customer {
                                    id
                                    email
                                    firstName
                                    lastName
                                }
                                userErrors {
                                    field
                                    message
                                }
                            }
                        }
                    ',
                'variables' => [
                    'email' => $email,
                    'password' => $password,
                    'firstName' => $firstName,
                    'lastName' => $lastName,
                    //'sendEmailInvitation' => $sendEmailInvitation
                ]
            ]);

            $responseData = $response->json();
            Log::info($responseData);
            Log::info($phone);

            if (isset($responseData['data']['customerCreate']['customer']['id'])) {
                $customerId = $responseData['data']['customerCreate']['customer']['id'] ?? null;
                $customerId = str_replace("gid://shopify/Customer/", "", $customerId);

                // add phone number to the customer
                $response = Http::withHeaders([
                    'X-Shopify-Access-Token' => $accessToken,
                    'Content-Type' => 'application/json',
                ])->put("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/{$customerId}.json", [
                    'customer' => [
                        'id' => $customerId,
                        'note' => 'Phone Number: ' . $phone
                    ]
                ]);
                // end of add phone number

                // Store customer to DB
                Customer::updateOrCreate(
                    ['shopify_customer_id' => $customerId],
                    [
                        'registered_from' => "Shopify",
                        'first_name' => $firstName,
                        'last_name' => $lastName,
                        'phone' => $phone,
                        'email' => $email,
                        'password' => $password,
                    ]
                );
                $return_response =  response()->json([
                    'code' => 'Success',
                    'message' => 'Customer registered successfully...',
                ]);
                return $return_response;
            }
        } catch (\Exception $e) {
            // Extract the response body
            $response = $e->getResponse();
            $responseBody = json_decode($response->getBody()->getContents(), true);

            // Check if 'errors' key exists
            if (isset($responseBody['errors'])) {
                // Extract the first error message (e.g., 'email has already been taken')
                $errorMessage = collect($responseBody['errors'])->flatten()->first();

                if (str_contains($errorMessage, 'has already been taken')) {
                    Log::info('Duplicate entry detected: ' . $errorMessage);
                    $return_response =  response()->json([
                        'code' => 422,
                        'message' => 'This email is already registered',
                    ]);
                    return $return_response;
                }
            } else {
                $errorMessage = $e->getMessage();
            }
            return $errorMessage;
        }
    }

    public function signupShopifyCustomerByOTP(Request $request)
    {
        // $email = $request->input('email');
        $email = "<EMAIL>";
        $customer_id = "*************";

        try {
            // Create account with email
            $signup_response = Http::withHeaders([
                'X-Shopify-Access-Token' => $this->accessToken,
                'Content-Type' => 'application/json',
            ])->post("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers.json", [
                'customer' => [
                    'email' => $email,
                    'accepts_marketing' => false,
                ]
            ]);

            // Activate account directly
            $activate_response = Http::withHeaders([
                'X-Shopify-Access-Token' => $this->accessToken,
                'Content-Type' => 'application/json',
            ])->post("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/{$customer_id}.json", [
                'customer' => [
                    "id" => $customer_id,
                    "state" => "enabled"
                ]
            ]);
            $data = $activate_response->json();

            Log::info($activate_response);
            exit;
            return $response->json();
        } catch (\Exception $e) {
            // Extract the response body
            $response = $e->getResponse();
            $responseBody = json_decode($response->getBody()->getContents(), true);

            // Check if 'errors' key exists
            if (isset($responseBody['errors'])) {
                // Extract the first error message (e.g., 'email has already been taken')
                $errorMessage = collect($responseBody['errors'])->flatten()->first();

                if (str_contains($errorMessage, 'has already been taken')) {
                    Log::info('Duplicate entry detected: ' . $errorMessage);
                    $return_response =  response()->json([
                        'code' => 422,
                        'message' => 'This email is already registered',
                    ]);
                    return $return_response;
                }
            } else {
                $errorMessage = $e->getMessage();
            }
            return $errorMessage;
        }
    }


    public function createShopifyCustomer(Request $request)
    {

        // Log::info($request);

        $client = new Client([
            'base_uri' => "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/",
            'auth' => ["{$this->apiKey}", "{$this->password}"],
        ]);

        try {
            // Create Customer to Shopify
            $responseCustomer = $client->post('customers.json', [
                'json' => [
                    'customer' => [
                        "registered_from" => "Shopify",
                        "first_name" => $request->input('first_name'),
                        "last_name" => $request->input('last_name'),
                        "email" => $request->input('email'),
                        "phone" => $request->input('phone'),
                        "send_email_invite" => false, // Not required but can be added
                        "send_email_welcome" => true, // Not required for creation without password
                        'verified_email' => true, // Marks email as verified
                        "password" => $request->input('password'),
                        "password_confirmation" => $request->input('password'),
                        'tags' => 'Shopify Customer',
                    ]
                ]
            ]);
            Log::info("Response for created customer to shopify...");
            // Log::info($responseCustomer);
            $responseData = json_decode($responseCustomer->getBody()->getContents(), true);

            if (isset($responseData['customer'])) {
                $customerId = $responseData['customer']['id'] ?? null;
                // Log::info($response['customer']);
                // Create a new customer using mass assignment
                Customer::create(
                    [
                        'shopify_customer_id' => $customerId,
                        'registered_from' => "Shopify",
                        'first_name' => $request->input('first_name'),
                        'last_name' => $request->input('last_name'),
                        'phone' => $request->input('phone'),
                        'email' => $request->input('email'),
                        'password' => $request->input('password'),
                        'shopify_status' => 'Registered',
                    ]
                );

                // ------- Send link to setup stripe payment ----------
                // $custDet = Customer::where('shopify_customer_id', $customerId)->first();
                // Log::info('Customer Detail : ');
                // Log::info($custDet);
                // if($custDet && $custDet[0]['stripe_customer_id']==''){
                //     Log::info("Stripe customer creating...");
                //     $stripeCustomer = $this->createCustomerByEmail($customerId, $custDet[0]['email'], $custDet[0]['first_name'] . " " . $custDet[0]['last_name']);
                // }
                // --------------End of send link to setup stripe payment------

                $return_response =  response()->json([
                    'code' => 0,
                    'message' => 'Success',
                ]);
            }
            return $return_response;
        } catch (\Exception $e) {
            // Extract the response body
            $response = $e->getResponse();
            $responseBody = json_decode($response->getBody()->getContents(), true);

            // Check if 'errors' key exists
            if (isset($responseBody['errors'])) {
                // Extract the first error message (e.g., 'email has already been taken')
                $errorMessage = collect($responseBody['errors'])->flatten()->first();

                if (str_contains($errorMessage, 'has already been taken')) {
                    Log::info('Duplicate entry detected: ' . $errorMessage);
                    $return_response =  response()->json([
                        'code' => 422,
                        'message' => 'This email is already registered',
                    ]);
                    return $return_response;
                }
            } else {
                $errorMessage = $e->getMessage();
            }
            return $errorMessage;
        }
    }

    // Product related functions
    public function countTotalProducts()
    {
        $response = $this->client->get('products/count.json');
        $total_products = json_decode($response->getBody()->getContents(), true);
        $totalProducts = $total_products['count'];
        return $totalProducts;
    }

    public function getProducts()
    {
        $response = $this->client->get('products.json?limit=20');
        $products = json_decode($response->getBody()->getContents(), true)['products'];
        // echo "<pre>"; print_r($products); exit;
        return $products;
    }

    public function productDetail($productId)
    {
        $response = $this->client->get("products/{$productId}.json");
        $productData = json_decode($response->getBody()->getContents(), true);
        $sku = $this->getSku($productData); // Extract SKU from variants
        return $productData;
    }

    private function extractNextPageInfo($linkHeader)
    {
        if (isset($linkHeader[0])) {
            $links = explode(',', $linkHeader[0]);
            foreach ($links as $link) {
                if (strpos($link, 'rel="next"') !== false) {
                    $url = trim(explode(';', $link)[0], '<>');
                    $parsedUrl = parse_url($url);
                    parse_str($parsedUrl['query'], $queryParams);
                    return $queryParams['page_info'] ?? null;
                }
            }
        }
        return null;
    }

    public function getProductsByRange($limit, $page_info)
    {
        $url = 'products.json?limit=' . $limit;
        if ($page_info) {
            $url .= '&page_info=' . urlencode($page_info);
        }

        $response = $this->client->get($url);
        $body = json_decode($response->getBody()->getContents(), true);

        return [
            'products' => $body['products'],
            'pagination' => $this->extractPaginationInfo($response)
        ];
    }

    protected function extractPaginationInfo($response)
    {
        $link_header = $response->getHeader('Link');
        $pagination = [];

        if ($link_header) {
            $links = explode(',', $link_header[0]);

            foreach ($links as $link) {
                $segments = explode(';', $link);
                $url = trim($segments[0], '<> ');
                $rel = trim($segments[1], ' rel=" ');

                parse_str(parse_url($url, PHP_URL_QUERY), $query_params);
                $pagination[$rel] = $query_params['page_info'] ?? null;
            }
        }
        return $pagination;
    }

    public function getProduct($productId)
    {
        $response = $this->client->get("products/{$productId}.json");
        $productData = json_decode($response->getBody()->getContents(), true)['product'];

        $sku = $this->getSku($productData); // Extract SKU from variants
        return $productData;
    }

    private function getSku($productData)
    {
        // Assuming the SKU is in the variants, and taking the first variant's SKU
        return isset($productData['variants'][0]['sku']) ? $productData['variants'][0]['sku'] : null;
    }

    public function productExists($productId)
    {
        try {
            $response = $this->client->get("products/{$productId}.json");
            return $response->getStatusCode() === 200;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function deleteProduct($productId)
    {
        // echo $productId; exit;
        $response = $this->client->delete("/admin/api/" . $this->apiVersion . "/products/{$productId}.json");
        // echo "<pre>"; print_r($response); echo "</pre>";
    }

    public function deleteDuplicateProducts($productId)
    {
        $this->client->delete("/admin/api/" . SHOPIFY_API_VERSION . "/products/{$productId}.json");
    }

    public function countTotalCollections()
    {
        $response = $this->client->get('smart_collections/count.json');
        $total_collections = json_decode($response->getBody()->getContents(), true);
        $totalCollections = $total_collections['count'];
        echo "Total Collections : " . $totalCollections;
        exit;
    }

    public function getSmartCollections()
    {
        $response = $this->client->get('smart_collections.json?limit=250');
        $collections = json_decode($response->getBody()->getContents(), true)['smart_collections'];
        echo "<pre>";
        print_r($collections);
        exit;
        return $collections;
    }

    public function getCustomCollections()
    {
        $response = $this->client->get('custom_collections.json');
        $collections = json_decode($response->getBody()->getContents(), true)['custom_collections'];
        // echo "<pre>"; print_r($collections); exit;
        return $collections;
    }

    public function listAllCollections()
    {
        $collections = Collection::query()->get()->toArray();
        echo "<pre>";
        print_r($collections);
        exit;
        return $collections;
    }

    public function storeSmartCollection()
    {
        $smartCollections = [];
        $url = 'smart_collections.json';

        do {
            $response = $this->client->get($url);
            $data = json_decode($response->getBody(), true);
            $smartCollections = array_merge($smartCollections, $data['smart_collections']);

            $headers = $response->getHeader('Link');
            $linkHeader = isset($headers[0]) ? $headers[0] : '';
            $nextPageUrl = ''; // Default to no next page

            // Extract the next page URL from the Link header
            if (preg_match('/<([^>]+)>; rel="next"/', $linkHeader, $matches)) {
                $nextPageUrl = $matches[1];
            }

            $url = $nextPageUrl ? str_replace("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/", '', $nextPageUrl) : null;
        } while ($url);

        // echo "<pre>"; print_r($smartCollections); echo "</pre>"; exit;
        $n = 1;
        foreach ($smartCollections as $collection) {
            $collections = Collection::select('id', 'shopify_collection_id')->where('shopify_collection_id', $collection['id'])->get()->toArray();
            echo $n . "<br/>" . "<pre>";
            print_r($collections);
            echo "</pre>";
            if (count($collections) > 0) {
                $collection_status = 'Updated Collection';
            } else {
                $collection_status = 'New Collection';
            }
            echo $collection_status . "<hr/>";

            Collection::updateOrCreate(
                ['shopify_collection_id' => $collection['id']],
                [
                    'title' => $collection['title'],
                    'collection_type' => 'smart_collection',
                    'collection_data' => json_encode($collection),
                    'collection_status' => $collection_status
                ]
            );
        }
        $n++;
    }

    public function storeCustomCollection()
    {
        $customCollections = [];
        $url = 'custom_collections.json';

        do {
            $response = $this->client->get($url);
            $data = json_decode($response->getBody(), true);
            $customCollections = array_merge($customCollections, $data['custom_collections']);

            $headers = $response->getHeader('Link');
            $linkHeader = isset($headers[0]) ? $headers[0] : '';
            $nextPageUrl = ''; // Default to no next page

            // Extract the next page URL from the Link header
            if (preg_match('/<([^>]+)>; rel="next"/', $linkHeader, $matches)) {
                $nextPageUrl = $matches[1];
            }

            $url = $nextPageUrl ? str_replace("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/", '', $nextPageUrl) : null;
        } while ($url);

        // echo "<pre>"; print_r($customCollections); echo "</pre>"; exit;
        $n = 1;
        foreach ($customCollections as $collection) {
            $collections = Collection::select('id', 'shopify_collection_id')->where('shopify_collection_id', $collection['id'])->get()->toArray();
            echo $n . "<br/>" . "<pre>";
            print_r($collections);
            echo "</pre>";
            if (count($collections) > 0) {
                $collection_status = 'Updated Collection';
            } else {
                $collection_status = 'New Collection';
            }
            echo $collection_status . "<hr/>";

            Collection::updateOrCreate(
                ['shopify_collection_id' => $collection['id']],
                [
                    'title' => $collection['title'],
                    'collection_type' => 'custom_collection',
                    'collection_data' => json_encode($collection),
                    'collection_status' => $collection_status
                ]
            );
        }
        $n++;
    }

    public function getAllCollections()
    {
        $collections = Collection::select('shopify_collection_id', 'title', 'collection_type', 'collection_data')
            ->get()
            ->toArray();
        // $collections = json_decode($response->getBody()->getContents(), true);
        // echo "<pre>"; print_r($collections); exit;
        $collections_array = [];
        $n = 0;
        foreach ($collections as $collection) {
            $collections_array[$n]['collection_id'] = $collection['shopify_collection_id'];
            $collections_array[$n]['title'] = $collection['title'];
            $collections_array[$n]['collection_type'] = $collection['collection_type'];
            $collections_array[$n]['collection_data'] = json_decode($collection['collection_data'], true);
            $n++;
        }
        // echo "<pre>"; print_r($collections_array); echo "</pre>"; exit;

        $json = json_encode($collections_array);

        // Output the JSON
        echo $json;
        // return $collections;
    }

    public function getProductsCollectionsByLimit()
    {
        $product_collections_inprogress = Product::where('shopify_status', 'Shopify-Collections-Inprogress')->get()->toArray();
        if (count($product_collections_inprogress) == 0) {
            Product::query()->update(['shopify_status' => 'Shopify-Collections-Inprogress']);
        }

        //echo $limit; exit;
        $limit = 50;
        for ($n = 1; $n <= 100; $n++) { // considering max 5000 products
            $product_list = Product::orderBy('id', 'asc')
                ->select('id', 'shopify_product_id', 'title', 'shopify_status')
                ->Where('shopify_status', '!=', 'Removed')
                ->Where('shopify_status', '!=', 'Collections not found')
                ->where(function ($query) {
                    $query->whereNull('collections')
                        ->orWhere('collections', '');
                })
                ->limit($limit) // Limit the number of results to 10
                ->get();

            $json = json_encode($product_list);
            $products = json_decode($json, true);

            if (count($products) == 0) {
                break;
            }

            for ($p = 0; $p < count($products); $p++) {
                $productId = $products[$p]['shopify_product_id'];
                $response = $this->getProductCollections($productId);
                if ($response != NULL) {
                    $collections = json_decode($response, true)['collections'];
                    Product::where('shopify_product_id', $productId)->update(['collections' => $collections, 'shopify_status' => 'Shopify-Collections-Fetched']);
                    echo "<pre>";
                    print_r($response);
                    echo "</pre><hr/>";
                } else {
                    Product::where('shopify_product_id', $productId)->update(['collections' => '', 'shopify_status' => 'Collections not found']);
                }
            }
        }
        // return $collections;
    }

    // Graphql
    public function getProductCollections($productId)
    {
        // Get Shopify credentials from .env
        $storeName = env('SHOPIFY_STORE_NAME');
        $accessToken = env('SHOPIFY_API_PASSWORD');

        // GraphQL endpoint
        $url = "https://$storeName.myshopify.com/admin/api/2023-04/graphql.json";

        // GraphQL query for product details
        $query = <<<GRAPHQL
        {
          product(id: "gid://shopify/Product/$productId") {
            id
            title
            collections(first: 100) {
              edges {
                node {
                  id
                  title
                }
              }
            }
            descriptionHtml
            vendor
            productType
            tags
            options {
              id
              name
              values
            }
            variants(first: 10) {
              edges {
                node {
                  id
                  title
                  sku
                  price
                }
              }
            }
            images(first: 10) {
              edges {
                node {
                  id
                  src
                  altText
                }
              }
            }
          }
        }
        GRAPHQL;

        // Make request to Shopify GraphQL API
        // $client = new Client();
        $response = $this->client->post($url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => $accessToken,
            ],
            'json' => ['query' => $query],
        ]);
        // echo "<pre>"; print_r($response); exit;

        // Handle the response
        if ($response->getStatusCode() == 200) {
            $data = json_decode($response->getBody(), true);
            // $collections = $data['data']['product']['collections']['edges'];
            $collections = $data['data']['product'];
            // echo "<pre>"; print_r($collections); exit;
            // return $product_data;

            // Initialize a new array to hold the transformed data
            $transformedCollections = [];

            // Iterate through each 'edges' element and extract 'node' data
            if (isset($collections['collections']['edges'])) {
                foreach ($collections['collections']['edges'] as $edge) {
                    $transformedCollections[] = $edge['node'];
                }
                // Convert and output JSON
                $json_data = $this->convertToJSON($transformedCollections);
                $json_array = array('collections' => $json_data);
                // echo $json; exit;

                // Output the transformed array
                return json_encode($json_array);
            } else {
                return NULL;
            }
            // echo "<pre>"; print_r($transformedCollections); exit;
            //return view('product_collections', compact('product_data'));

        } else {
            // Handle error
            $error = "GraphQL query failed: " . $response->getBody();
            return view('error', compact('error'));
        }
    }

    public function fetchAllShopifyCollections() // get all collections with Rest API
    {
        $all_collections = [];

        // ============= Smart collections ================
        $smartCollections = [];
        $url = 'smart_collections.json';

        do {
            $response = $this->client->get($url);
            $data = json_decode($response->getBody(), true);
            $smartCollections = array_merge($smartCollections, $data['smart_collections']);
            $headers = $response->getHeader('Link');
            $linkHeader = isset($headers[0]) ? $headers[0] : '';
            $nextPageUrl = ''; // Default to no next page
            if (preg_match('/<([^>]+)>; rel="next"/', $linkHeader, $matches)) {
                $nextPageUrl = $matches[1];
            }

            $url = $nextPageUrl ? str_replace("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/", '', $nextPageUrl) : null;
        } while ($url);

        $n = 0;
        $smart_collections = [];
        foreach ($smartCollections as $collection) {
            $smart_collections[$n]['id'] = $collection['id'];
            $smart_collections[$n]['title'] = $collection['title'];
            $smart_collections[$n]['type'] = "Smart";
            $smart_collections[$n]['category'] = $this->getMetafieldsByCollectionId($collection['id'], 'Smart');
            $n++;
        }

        // ============= custom collections ==============
        $customCollections = [];
        $url = 'custom_collections.json';

        do {
            $response = $this->client->get($url);
            $data = json_decode($response->getBody(), true);
            $customCollections = array_merge($customCollections, $data['custom_collections']);
            $headers = $response->getHeader('Link');
            $linkHeader = isset($headers[0]) ? $headers[0] : '';
            $nextPageUrl = ''; // Default to no next page
            if (preg_match('/<([^>]+)>; rel="next"/', $linkHeader, $matches)) {
                $nextPageUrl = $matches[1];
            }

            $url = $nextPageUrl ? str_replace("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/", '', $nextPageUrl) : null;
        } while ($url);

        $n = 0;
        $custom_collections = [];
        foreach ($customCollections as $collection) {
            $custom_collections[$n]['id'] = $collection['id'];
            $custom_collections[$n]['title'] = $collection['title'];
            $custom_collections[$n]['type'] = "Custom";
            $custom_collections[$n]['category'] = $this->getMetafieldsByCollectionId($collection['id'], 'Custom');
            $n++;
        }
        $all_collections = array_merge($smart_collections, $custom_collections);
        $collection_json = json_encode($all_collections);

        $categories = [];
        foreach ($all_collections as $collection) {
            if (isset($collection['category'])) {
                if (!in_array($collection['category'], $categories)) {
                    $categories[] = $collection['category'];
                }
            }
        }
        $categories_json = json_encode($categories);
        echo "Categories : " . $categories_json . "<hr/>";
        echo "Collections : " . $collection_json . "<br/>";
    }

    public function getMetafieldsByCollectionId($collectionId, $collectionType) // REST API
    {
        // For custom collections
        if ($collectionType == "Smart") {
            $url = "https://{$this->storeName}.myshopify.com/admin/api/$this->apiVersion/smart_collections/{$collectionId}/metafields.json";
        } else {
            $url = "https://{$this->storeName}.myshopify.com/admin/api/$this->apiVersion/custom_collections/{$collectionId}/metafields.json";
        }
        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->accessToken,
        ])->get($url);

        if ($response->successful()) {
            $metafields_response = response()->json($response->json());
            $response = new JsonResponse(['metafields' => $metafields_response]);
            $jsonContent = $response->getContent();
            $responseArray = json_decode($jsonContent, true);
            if (isset($responseArray['metafields']['original']['metafields']) && count($responseArray['metafields']['original']['metafields']) > 0) {
                return $responseArray['metafields']['original']['metafields'][0]['value'];
            } else {
                return null;
            }
        } else {
            // return response()->json(['error' => $response->body()], $response->status());
            return null;
        }
    }

    public function fetchAllCollections() // Fetch collections with GraphQL
    {
        // GraphQL endpoint
        $url = "https://$this->storeName.myshopify.com/admin/api/$this->apiVersion/graphql.json";
        $category = "Cat";

        // GraphQL query for product details
        $query = <<<GRAPHQL
        {
          collections(first: 250) {
                edges {
                    node {
                        id
                        title
                        metafields(first: 10) {
                            edges {
                                node {
                                    namespace
                                    key
                                    value
                                }
                            }
                        }
                    }
                }
            }
        }
        GRAPHQL;

        // Make request to Shopify GraphQL API
        // $client = new Client();
        $response = $this->client->post($url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => $this->accessToken,
            ],
            'json' => ['query' => $query],
        ]);
        // echo "<pre>"; print_r($response); exit;

        // Handle the response
        if ($response->getStatusCode() == 200) {
            $data = json_decode($response->getBody(), true);


            $collections = $data['data']['collections']['edges'];

            // Initialize a new array to hold the transformed data
            $transformedCollections = [];

            if ($collections) {
                $n = 0;
                foreach ($collections as $collection) {
                    $transformedCollections[$n]['id'] = $collection['node']['id'];
                    $transformedCollections[$n]['title'] = $collection['node']['title'];

                    if (isset($collection['node']['metafields']['edges']) && count($collection['node']['metafields']['edges']) > 0) {
                        $transformedCollections[$n]['category'] = $collection['node']['metafields']['edges'][0]['node']['value'];
                    }
                    $n++;
                }

                // echo "<pre>"; print_r($transformedCollections); echo "</pre>";

                $json_data = $this->convertToJSON($transformedCollections);
                $json_array = array('collections' => $json_data);
                // echo $json; exit;

                echo "<pre>";
                print_r($json_array);
                echo "</pre>";
                exit;

                // Output the transformed array
                return json_encode($json_array);
            } else {
                return NULL;
            }
        } else {
            // Handle error
            $error = "GraphQL query failed: " . $response->getBody();
            return view('error', compact('error'));
        }
    }

    // Function to remove prefix and convert to JSON
    function convertToJSON($array)
    {
        $result = array_map(function ($item) {
            // Remove prefix "gid://shopify/Collection/"
            $item['id'] = str_replace('gid://shopify/Collection/', '', $item['id']);
            return $item;
        }, $array);

        // Convert array to JSON
        return json_encode($result);
    }

    public function renameHandles($limit_from, $limit_to)
    {
        $product_list = Product::orderBy('id', 'asc')
            ->select('id', 'shopify_product_id', 'handle', 'shopify_status')
            ->where('shopify_status', 'New Product')
            ->where('updated_handle', '')
            ->skip($limit_from)
            ->take($limit_to - $limit_from)
            ->get();

        $json = json_encode($product_list);
        $products = json_decode($json, true);
        // echo "<pre>"; print_r($products); exit;

        $handleDet = '';
        foreach ($products as $product) {
            $originalHandle = $product['handle'];
            $cleanHandle = $this->removeSuffix($originalHandle);

            if ($cleanHandle !== $originalHandle && !$this->handleExists($cleanHandle)) {
                $handleDet .=  "Handle Available...<br/><br/>";

                $this->updateProductHandle($product['shopify_product_id'], $cleanHandle);
                Product::where('shopify_product_id', $product['shopify_product_id'])->update(['updated_handle' => $cleanHandle]);
            } else {
                return "Handle already Exists...<br/><hr/><br/>";
            }
        }
    }

    public function updateHandles($productId)
    {
        $product_list = Product::orderBy('id', 'asc')
            ->select('id', 'shopify_product_id', 'handle', 'updated_handle', 'shopify_status')
            ->where('shopify_product_id', $productId)
            ->get();

        $json = json_encode($product_list);
        $products = json_decode($json, true);
        // echo "<pre>"; print_r($products); exit;

        $handleDet = '<br/>-------------<br/>';
        foreach ($products as $product) {
            $originalHandle = $product['handle'];
            $cleanHandle = $this->removeSuffix($originalHandle);

            $handleDet .= $product['shopify_product_id'] . " : <br>";
            $handleDet .=  "Original URL : " . $product['handle'] . "<br>";
            $handleDet .=  "Renamed URL : " . $cleanHandle . "<br/>";

            if ($cleanHandle !== $originalHandle && !$this->handleExists($cleanHandle)) {
                $handleDet .=  "Handle Available...<br/><br/>";

                $this->updateProductHandle($product['shopify_product_id'], $cleanHandle);
                Product::where('shopify_product_id', $product['shopify_product_id'])->update(['handle' => $cleanHandle, 'updated_handle' => $originalHandle]);
            } else {
                $handleDet .=  "Handle already Exists...<br/><hr/><br/>";
            }
        }
        return $handleDet;
    }

    protected function removeSuffix($handle)
    {
        return preg_replace('/-\d+$/', '', $handle);
    }

    protected function handleExists($handle)
    {
        $response = $this->client->get("products.json?handle={$handle}", [
            'headers' => [
                'Content-Type' => 'application/json'
            ]
        ]);

        $data = json_decode($response->getBody(), true);

        return !empty($data['products']);
    }

    protected function updateProductHandle($productId, $newHandle)
    {
        $payload = [
            'product' => [
                'id' => $productId,
                'handle' => $newHandle
            ]
        ];

        $response = $this->client->put("products/{$productId}.json", [
            'json' => $payload,
            'headers' => [
                'Content-Type' => 'application/json'
            ]
        ]);
        return $response->getStatusCode() == 200;
    }

    public function updateVariantPriceBySku($productId, $variantId, $sku, $newPrice, $compareAtPrice, $newWeight)
    {
        // $client = new Client();
        // $baseUrl = "https://{$this->shopifyStoreDomain}/admin/api/2023-07";

        // Fetch all products to find the variant by SKU
        $productsUrl = "products.json";

        try {
            // $response = $this->client->get("products.json");
            // $products = json_decode($response->getBody(), true)['products'];

            // Find the variant ID by SKU
            /* $variantId = null;
                foreach ($products as $product) {
                    foreach ($product['variants'] as $variant) {
                        if ($variant['sku'] === $sku) {
                            $variantId = $variant['id'];
                            // break 2;
                        }
                    }
                } */

            if ($variantId) {
                // Update the variant price
                $updateUrl = "variants/{$variantId}.json";

                $updateResponse = $this->client->put($updateUrl, [
                    'json' => [
                        'variant' => [
                            'id' => $variantId,
                            'price' => $newPrice,
                            'compare_at_price' => $compareAtPrice,
                            'weight' => $newWeight
                        ]
                    ]
                ]);

                if ($updateResponse->getStatusCode() == 200) {
                    return response()->json(['message' => 'Variant price updated successfully.']);
                } else {
                    return response()->json(['message' => 'Failed to update variant price.'], $updateResponse->getStatusCode());
                }
            } else {
                return response()->json(['message' => 'Variant with SKU not found.'], 404);
            }
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            return response()->json(['message' => 'An error occurred: ' . $e->getMessage()], 500);
        } catch (\Exception $e) {
            return response()->json(['message' => 'An unexpected error occurred: ' . $e->getMessage()], 500);
        }
    }

    public function updateVariantWeightBySku($productId, $variantId, $sku, $weight, $newWeight)
    {
        // Fetch all products to find the variant by SKU
        $productsUrl = "products.json";
        try {
            if ($variantId) {
                // Update the variant price
                $updateUrl = "variants/{$variantId}.json";

                $updateResponse = $this->client->put($updateUrl, [
                    'json' => [
                        'variant' => [
                            'id' => $variantId,
                            'weight' => $newWeight
                        ]
                    ]
                ]);

                if ($updateResponse->getStatusCode() == 200) {
                    return response()->json(['message' => 'Variant price updated successfully.']);
                } else {
                    return response()->json(['message' => 'Failed to update variant price.'], $updateResponse->getStatusCode());
                }
            } else {
                return response()->json(['message' => 'Variant with SKU not found.'], 404);
            }
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            return response()->json(['message' => 'An error occurred: ' . $e->getMessage()], 500);
        } catch (\Exception $e) {
            return response()->json(['message' => 'An unexpected error occurred: ' . $e->getMessage()], 500);
        }
    }

    public function updateDescription()
    {

        $products = Product::select('id', 'shopify_product_id', 'body_html')
            ->where('tags', '')
            ->where('collections', '')
            ->where('sales_channels', '')
            ->where('product_data', '')
            ->whereNull('handle')
            //->limit(500)
            ->get()
            ->toArray();

        // echo "<pre>"; print_r($products); echo "</pre>"; exit;
        foreach ($products as $product) {

            // echo $product['shopify_product_id'] . "<br/>"; continue;
            // $encodedHtml = '&lt;p&gt;This is a paragraph.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Item 1&lt;/li&gt;&lt;li&gt;Item 2&lt;/li&gt;&lt;/ul&gt;';
            $decodedHtml = html_entity_decode($product['body_html']);
            echo $decodedHtml;

            $payload = [
                'product' => [
                    'id' => $product['shopify_product_id'],
                    'body_html' => $decodedHtml,
                ]
            ];

            $response = $this->client->put("products/{$product['shopify_product_id']}.json", [
                'json' => $payload
            ]);

            Product::where('shopify_product_id', $product['shopify_product_id'])
                ->update([
                    'shopify_status' => 'Description Updated'
                ]);

            // return json_decode($response->getBody()->getContents(), true);
        }
    }

    public function getAllSalesChannes()
    {
        try {
            $response = $this->client->get('sales_channels.json');

            // Check if the request was successful
            if ($response->getStatusCode() !== 200) {
                throw new Exception('Failed to fetch sales channels. Status code: ' . $response->getStatusCode());
            }

            // Decode the JSON response
            $data = json_decode($response->getBody(), true);

            // Check if the 'sales_channels' key exists
            if (!isset($data['sales_channels'])) {
                throw new Exception('Sales channels data not found in response.');
            }

            // Extract sales channels
            $salesChannels = $data['sales_channels'];

            // Output sales channels for debugging
            echo "<pre>";
            print_r($salesChannels);
            echo "</pre>";
        } catch (Exception $e) {
            // Handle any errors that occurred
            echo 'Error: ' . $e->getMessage();
        }
        exit;
    }

    public function getAllDiscounts()
    {
        try {
            $response = $this->client->get('price_rules.json');
            $discount_response = json_decode($response->getBody()->getContents(), true);

            $priceRules = $discount_response['price_rules'];

            // echo "<pre>"; print_r($priceRules); echo "</pre>";

            foreach ($priceRules as $pricerule) {
                // echo $pricerule['title'] . "<br/>";
                $priceRuleArray = json_encode($pricerule);
                $discount_added = DB::table('discounts')->updateOrInsert(
                    ['discount_id' => $pricerule['id']],
                    [
                        'title' => $pricerule['title'],
                        'discount_type' => $pricerule['value_type'],
                        'discount_amount' => $pricerule['value'],
                        'target_type' => $pricerule['target_type'],
                        'usage_limit' => $pricerule['usage_limit'],
                        'once_per_customer' => $pricerule['once_per_customer'],
                        'customer_selection' => $pricerule['customer_selection'],
                        'starts_at' => $pricerule['starts_at'],
                        'ends_at' => $pricerule['ends_at'],
                        'discount_data' => $priceRuleArray
                    ]
                );
            }
        } catch (Exception $e) {
            // Handle any errors that occurred
            echo 'Error: ' . $e->getMessage();
        }
    }

    function updateDiscountForCustomers($discountId, $customerId, $discount_tags)
    {
        $url = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/graphql.json";

        $priceRuleId = $discountId;

        // $query = <<<GRAPHQL
        // mutation {
        //     priceRuleUpdate(id: "gid://shopify/PriceRule/{$priceRuleId}", priceRule: {
        //         customerSelection: PREREQUISITE,
        //         prerequisiteCustomerIds: ["gid://shopify/Customer/{$customerId}"]
        //     }) {
        //         priceRule {
        //             id
        //             customerSelection
        //             prerequisiteCustomerIds
        //         }
        //         userErrors {
        //             field
        //             message
        //         }
        //     }
        // }
        // GRAPHQL;

        // $response = $this->client->post($url, [
        //     'headers' => [
        //         'Content-Type' => 'application/json',
        //         'X-Shopify-Access-Token' => $this->accessToken,
        //     ],
        //     'json' => ['query' => $query],
        // ]);

        // update customers for discounts to shopify with REST API
        // $response = $this->client->put("price_rules/{$priceRuleId}.json", [
        //     'json' => [
        //         'price_rule' => [
        //             'customer_selection' => 'prerequisite',
        //             'prerequisite_customer_ids' => [$customerId] // Assign customer to discount
        //         ]
        //     ]
        // ]);

        // Update customer with new tags to shopify
        $response = $this->client->get("customers/{$customerId}.json");
        $customer = json_decode($response->getBody()->getContents(), true)['customer'];
        $tagString = $discount_tags;

        $updateData = [
            'customer' => [
                'id' => $customerId,
                'tags' => $tagString
            ]
        ];
        $updateResponse = $this->client->put("customers/{$customerId}.json", [
            'json' => $updateData
        ]);

        return json_decode($updateResponse->getBody()->getContents(), true);
    }

    public function addProductToShopify()
    {
        try {
            $productData = [
                'title' => 'Test Product Added From APP',
                'body_html' => '<strong>Good product!</strong>',
                'vendor' => 'Vendor Name',
                'product_type' => 'Type',
                'variants' => [
                    [
                        'option1' => 'First Variant',
                        'price' => '19.99',
                        'sku' => 'variant-sku-1',
                        'barcode' => '123456789012',
                        'compare_at_price' => '29.99',
                        'wholesale' => '15.00',
                        'shipping_weight_lbs' => '1.5',
                        'minimum_advertised_price' => '18.00'
                    ],
                    [
                        'option1' => 'Second Variant',
                        'price' => '24.99',
                        'sku' => 'variant-sku-2',
                        'barcode' => '123456789013'
                    ]
                ],
                'images' => [
                    [
                        'src' => 'https://example.com/image1.jpg'
                    ],
                    [
                        'src' => 'https://example.com/image2.jpg'
                    ]
                ]
            ];

            $response = $this->client->post("products.json", [
                'json' => ['product' => $productData]
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            return response()->json(['message' => 'An error occurred: ' . $e->getMessage()], 500);
        } catch (\Exception $e) {
            return response()->json(['message' => 'An unexpected error occurred: ' . $e->getMessage()], 500);
        }
    }

    public function addVariantToProduct($productId, $variantData)
    {
        $response = $this->client->post("products/{$productId}/variants.json", [
            'json' => ['variant' => $variantData]
        ]);

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getVariantIdBySKU($sku)
    {

        $sku = (string) $sku;

        $url = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/graphql.json";

        // GraphQL query for product details
        $query = <<<'GRAPHQL'
            query ($sku: String!) {
                productVariants(first: 1, query: $sku) {
                    edges {
                        node {
                            id
                        }
                    }
                }
            }
        GRAPHQL;

        $client = new Client([
            'base_uri' => "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/",
            'auth' => ["{$this->apiKey}", "{$this->password}"],
        ]);
        $response = $client->post($url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => $this->accessToken,
            ],
            'json' => [
                'query' => $query,
                'variables' => [
                    'sku' => $sku
                ]
            ],
        ]);
        // Log::info("executing variant by sku function..."); 
        $body = json_decode($response->getBody()->getContents(), true);
        $edges = $body['data']['productVariants']['edges'];

        if (!empty($edges)) {
            $variantId = $this->extractIdFromGid($edges[0]['node']['id']);
            return $variantId;
        } else {
            echo "Variant not found";
        }
    }

    public function getProductSalesChannles($productId)
    {
        // echo "Product id : " . $productId; exit;
        // $response = $this->client->get("products/{$productId}/publications.json")->body->publications;

        $url = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/graphql.json";

        $query = <<<GRAPHQL
        {
            product(id: "gid://shopify/Product/$productId") {
                resourcePublications(first: 10) {
                    nodes {
                        isPublished
                        publication {
                            name
                            id
                        }
                    }
                }
            }
        }
        GRAPHQL;

        $response = $this->client->post($url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => $this->accessToken,
            ],
            'json' => ['query' => $query],
        ]);
        // echo "<pre>"; print_r($response); exit;

        if ($response->getStatusCode() == 200) {
            // echo $productId; exit;
            $data = json_decode($response->getBody(), true);
            // echo "<pre>"; print_r($data); echo "</pre>"; exit;
            $saleschannels = $data['data']['product'];
            $transformedChannels = [];

            if (isset($saleschannels['resourcePublications']['nodes'])) {
                foreach ($saleschannels['resourcePublications']['nodes'] as $edge) {
                    $transformedChannels[] = $edge;
                }

                if (count($transformedChannels) > 0) {
                    $channels = [];
                    for ($c = 0; $c < count($transformedChannels); $c++) {
                        $channels[$c]['channel'] = $transformedChannels[$c]['publication']['name'];
                        $channels[$c]['id'] = $this->extractIdFromGid($transformedChannels[$c]['publication']['id']);
                    }
                    $channels_detail = json_encode($channels);
                    return $channels_detail;
                } else {
                    return NULL;
                }
            } else {
                return NULL;
            }
        } else {
            return NULL;
        }
        return NULL;
    }

    public function getAllSalesChannles()
    {
        // echo "Product id : " . $productId; exit;
        // $response = $this->client->get("products/{$productId}/publications.json")->body->publications;

        // $url = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/sales_channels.json";

        $query = <<<GRAPHQL
            query {
            channels(first: 10) {
                edges {
                node {
                    id
                    name
                }
                }
            }
            }
            GRAPHQL;

        try {
            $response = $this->client->post("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/graphql.json", [
                'headers' => [
                    'X-Shopify-Access-Token' => $this->accessToken,
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode(['query' => $query]),
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            $response_data = $data['data']['channels']['edges'];
            foreach ($response_data as $channel) {
                $sales_channels[] = $channel['node'];
            }
            return $sales_channels;
            // echo "<pre>"; print_r($sales_channels); echo "</pre>"; exit;
            // return response()->json($data['data']['channels']['edges']);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function createCollections()
    {
        $collections = Collection::select('id', 'shopify_collection_id', 'title', 'collection_type', 'collection_data')
            //->where('collection_type','smart_collection')
            ->get()
            ->toArray();
        // echo "<pre>"; print_r($collections); echo "</pre>"; exit;
        $n = 1;
        foreach ($collections as $collection) {
            // echo $n . "<br/>";
            //echo $collection['id'] . " : " . $collection['shopify_collection_id'] . "<br/>"; continue;
            $colData = [];
            $colData = json_decode($collection['collection_data'], true);
            // echo "<pre>"; print_r($colData); echo "</pre>"; continue;
            // $n++; 
            // continue;

            // echo $collection['collection_type']."<br/>"; continue;

            if ($collection['collection_type'] == 'smart_collection') {
                if (isset($colData['image']) && !empty($colData['image'])) {
                    $colImage = [
                        'src' => $colData['image']['src'],
                        'alt' => $colData['image']['alt'],
                        'width' => $colData['image']['width'],
                        'height' => $colData['image']['height'],
                    ];
                } else {
                    $colImage = '';
                }

                $collectionData = [
                    'title' => $collection['title'],
                    'body_html' => $colData['body_html'],
                    'rules' => $colData['rules'],
                    'published' => true,
                    'handle' => $colData['handle'],
                    'sort_order' => $colData['sort_order'],
                    'disjunctive' => $colData['disjunctive'],
                    //'published_at' => '2023-08-12T01:03:31-07:00',
                    'published_scope' => $colData['published_scope'],
                    'template_suffix' => '',
                    'image' => $colImage,
                ];
                // echo "<pre>"; print_r($collectionData); echo "</pre>"; continue; 

                $response = $this->client->post("smart_collections.json", [
                    'json' => [
                        'smart_collection' => $collectionData,
                    ],
                ]);
                $mpb_collection = json_decode($response->getBody()->getContents(), true)['smart_collection'];
            } elseif ($collection['collection_type'] == 'custom_collection') {
                if (isset($colData['image']) && !empty($colData['image'])) {
                    $colImage = [
                        'src' => $colData['image']['src'],
                        'alt' => $colData['image']['alt'],
                        'width' => $colData['image']['width'],
                        'height' => $colData['image']['height'],
                    ];
                } else {
                    $colImage = '';
                }

                $collectionData = [
                    'title' => $collection['title'],
                    'body_html' => $colData['body_html'],
                    'published' => true,
                    'handle' => $colData['handle'],
                    'sort_order' => $colData['sort_order'],
                    //'disjunctive' => $colData['disjunctive'],
                    //'published_at' => '2023-08-12T01:03:31-07:00',
                    'published_scope' => $colData['published_scope'],
                    'template_suffix' => '',
                    //'rules' => $colData['rules'],
                    'image' => $colImage,
                ];
                // echo "<pre>"; print_r($collectionData); echo "</pre>"; continue;
                $response = $this->client->post("custom_collections.json", [
                    'json' => [
                        'custom_collection' => $collectionData,
                    ],
                ]);
                $mpb_collection = json_decode($response->getBody()->getContents(), true)['custom_collection'];
            }

            // echo "<pre>"; print_r($response); echo "</pre>"; continue;
            // echo "<pre>"; print_r($new_collection); echo "</pre>"; continue;

            // echo $mpb_collection['id']; continue;
            DB::table("collections")->where('id', $collection['id'])
                ->update(['shopify_collection_id' => $mpb_collection['id']]);


            // if ($response->successful()) {
            //     return response()->json([
            //         'message' => 'Smart Collection created successfully',
            //         'data' => $response->json()
            //     ]);
            // } else {
            //     return response()->json([
            //         'message' => 'Failed to create Smart Collection',
            //         'error' => $response->body()
            //     ]);
            // }
        }
    }

    public function updateCollection($id, $shopify_collection_id, $title)
    {
        $data = [
            'collection' => [
                'title' => $title
            ],
        ];
        $response = $this->client->put("collections/{$shopify_collection_id}", $data);
        //echo "<pre>"; print_r($response); exit;
    }

    public function updateTestCollection($shopify_collection_id, $title)
    {
        $data = [
            'collection' => [
                'title' => $title
            ],
        ];
        $response = $this->client->put("collections/{$shopify_collection_id}", $data);
        echo "<pre>";
        print_r($response);
        echo "</pre>";
        exit;
        $collection_response = $response->getBody()->getContents();
        return $collection_response;
    }


    function updateShopifyCollection($collectionId, $newTitle)
    {
        $globalId = base64_encode("gid://shopify/Collection/{$collectionId}");

        $endpoint = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/graphql.json"; // Replace with the correct Shopify API version
        $query = <<<'GRAPHQL'
            mutation collectionUpdate($id: ID!, $title: String) {
                collectionUpdate(input: { id: $id, title: $title }) {
                    collection {
                        id
                        title
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }
        GRAPHQL;

        $variables = [
            'id' => $globalId,
            'title' => $newTitle,
        ];

        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->accessToken,
            'Content-Type' => 'application/json',
        ])->post($endpoint, [
            'query' => $query,
            'variables' => $variables,
        ]);

        $collect_response = json_decode($response->getBody()->getContents(), true);
        // echo "<pre>"; print_r($collect_response); echo "</pre>"; exit;

        if ($response->successful()) {
            $data = $response->json();
            if (!empty($data['data']['collectionUpdate']['userErrors'])) {
                return [
                    'success' => false,
                    'errors' => $data['data']['collectionUpdate']['userErrors'],
                ];
            }
            return [
                'success' => true,
                'collection' => $data['data']['collectionUpdate']['collection'],
            ];
        } else {
            return [
                'success' => false,
                'errors' => $response->json(),
            ];
        }
    }


    public function addCustomCollectionsToProducts()
    {
        // For custom collection CAT
        $products = Product::select('id', 'title', 'shopify_product_id', 'collections')
            //->where('collections', 'like', '%{"id":"277847736496","title":"Cat"}%') // for Cat collection
            ->where('collections', 'like', '%{"id":"277847703728","title":"Dog"}%') // for Dog collection
            //->limit(100)
            ->get()
            ->toArray();
        // echo "<pre>"; print_r($products); echo "</pre>"; exit;
        foreach ($products as $product) {

            // $collectionId = 286525587516; // custom collection id for 'CAT' on MPB
            $collectionId = 286525620284; // custom collection id for 'DOG' on MPB

            $response = $this->client->get("collects.json", [
                'query' => [
                    'product_id' => $product['shopify_product_id'],
                ],
            ]);

            $collects = json_decode($response->getBody()->getContents(), true)['collects'];

            $alreadyInCollection = false;
            foreach ($collects as $collect) {
                if ($collect['collection_id'] == $collectionId) {
                    $alreadyInCollection = true;
                    break;
                }
            }

            if (!$alreadyInCollection) {
                $collectData = [
                    'collect' => [
                        'product_id' => $product['shopify_product_id'],
                        'collection_id' => $collectionId,
                    ],
                ];

                $response = $this->client->post("collects.json", [
                    'json' => $collectData,
                ]);
            }
            usleep(500000);
        }

        // $collections = json_decode($response->getBody()->getContents(), true)['collect'];

        // // Handle the response
        // if ($response->successful()) {
        //     return response()->json([
        //         'message' => 'Product assigned to collection successfully.',
        //         'data' => $response->json()['collect'],
        //     ], 201);
        // } else {
        //     return response()->json([
        //         'message' => 'Failed to assign product to collection.',
        //         'errors' => $response->json(),
        //     ], $response->status());
        // }
    }

    public function uploadImagesToProducts()
    {
        $products = Product::select('id', 'shopify_product_id', 'product_data')
            ->where('shopify_status', '!=', 'Images - Updated')
            ->limit(990)
            ->get()
            ->toArray();

        // echo "<pre>"; print_r($products); echo "</pre>"; exit;

        foreach ($products as $product) {
            if ($product['product_data']) {
                $productId = $product['shopify_product_id'];
                $productDet = json_decode($product['product_data'], true);
                $productIamges = $productDet['images'];
                echo $productId . "<br/>";
                // echo "<pre>"; print_r($productIamges); echo "</pre>"; continue;
                // echo "Original image source : " . $productDet['image']['src']."<br/>"; continue;
                foreach ($productIamges as $image) {
                    //echo "<pre>"; print_r($image); echo "</pre>";

                    if ($productDet['image']['src'] != $image['src']) {
                        $url = $image['src'];

                        // Extract the path from the URL
                        $path = parse_url($url, PHP_URL_PATH);

                        // Get the image name with the extension
                        $imageName = basename($path);

                        // Parse the query string to get the parameters
                        parse_str(parse_url($url, PHP_URL_QUERY), $queryParams);

                        if (isset($queryParams['v'])) {
                            $imageNameWithVersion = $imageName . '?v=' . $queryParams['v'];
                        } else {
                            $imageNameWithVersion = $imageName;
                        }
                        // echo $imageNameWithVersion."<br/>"; continue;

                        // $imagePath = storage_path("app/public/test_image/" . $imageNameWithVersion);
                        // $imagePath = storage_path('app/public/shopify_images/' . $imageName);
                        $imagePath = $url;

                        // // Read the image file and encode it in base64
                        $imageData = base64_encode(file_get_contents($imagePath));

                        // echo "<pre>"; print_r($imageData); echo "</pre>"; continue;

                        // Set up the payload
                        $payload = [
                            'image' => [
                                'attachment' => $imageData,
                                'filename' => $imageName, // The filename to use for the uploaded image
                            ]
                        ];

                        // Send the POST request to Shopify
                        $response = $this->client->post("products/{$productId}/images.json", [
                            'json' => $payload
                        ]);
                        usleep(500000);
                    }
                }
                echo "<hr/>";
            }
            Product::where('shopify_product_id', $product['shopify_product_id'])->update(['shopify_status' => 'Images - Updated']);
        }
        exit;
        // Handle the response
        // if ($response->successful()) {
        //     $imageDetails = $response->json();
        //     // You can now do something with the image details, like saving them to your database
        //     return response()->json(['success' => true, 'data' => $imageDetails]);
        // } else {
        //     return response()->json(['success' => false, 'error' => $response->body()]);
        // }
    }

    public function updateShopifyDiscount($discountId, $updatedData)
    {
        // echo "<pre>"; print_r($updatedData); echo "</pre>";
        // echo now()->toIso8601String(); exit;
        $startDate = Carbon::createFromFormat('d-m-Y H:i', $updatedData['price_rule']['starts_at'])->toIso8601String();
        $endDate = Carbon::createFromFormat('d-m-Y H:i', $updatedData['price_rule']['ends_at'])->toIso8601String();
        // echo $startDate . " : " . $endDate; exit; 
        $response = $this->client->put("https://{$this->storeName}.myshopify.com/admin/api/$this->apiVersion/price_rules/{$discountId}.json", [
            'headers' => [
                'X-Shopify-Access-Token' => $this->accessToken,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'price_rule' => [
                    'title' => $updatedData['price_rule']['title'],
                    'value' => $updatedData['price_rule']['value'],
                    'value_type' => $updatedData['price_rule']['value_type'],
                    'target_type' => $updatedData['price_rule']['target_type'],
                    'usage_limit' => $updatedData['price_rule']['usage_limit'],
                    'once_per_customer' => $updatedData['price_rule']['once_per_customer'],
                    'customer_selection' => $updatedData['price_rule']['customer_selection'],
                    'starts_at' => $startDate,
                    'ends_at' => $endDate,
                ]
            ]
        ]);

        // Profile update data into database
        DB::table('discounts')->where('discount_id', $discountId)->update([
            'title' => $updatedData['price_rule']['title'],
            'discount_type' => $updatedData['price_rule']['value_type'],
            'discount_amount' => $updatedData['price_rule']['value'],
            'target_type' => $updatedData['price_rule']['target_type'],
            'usage_limit' => $updatedData['price_rule']['usage_limit'],
            'once_per_customer' => $updatedData['price_rule']['once_per_customer'],
            'customer_selection' => $updatedData['price_rule']['customer_selection'],
            'starts_at' => $startDate,
            'ends_at' => $endDate
        ]);

        return $response;
    }

    public function updateGiveaway(Request $request)
    {
        // echo "<pre>"; print_r($updatedData); echo "</pre>";
        // echo now()->toIso8601String(); exit;
        $drawTime = Carbon::createFromFormat('d-m-Y H:i', $request['date_of_joining'])->toIso8601String();
        if ($request->hasFile('draw_image')) {
            $image = $request->file('draw_image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads'), $imageName);
        } else {
            $imageName = '';
        }
        // echo $startDate . " : " . $endDate; exit; 
        // $response = $this->client->put("https://{$this->storeName}.myshopify.com/admin/api/$this->apiVersion/price_rules/{$discountId}.json", [
        //     'headers' => [
        //         'X-Shopify-Access-Token' => $this->accessToken,
        //         'Content-Type' => 'application/json',
        //     ],
        //     'json' => [
        //         'price_rule' => [
        //             'title' => $updatedData['price_rule']['title'],
        //             'value' => $updatedData['price_rule']['value'],
        //             'value_type' => $updatedData['price_rule']['value_type'],
        //             'target_type' => $updatedData['price_rule']['target_type'],
        //             'usage_limit' => $updatedData['price_rule']['usage_limit'],
        //             'once_per_customer' => $updatedData['price_rule']['once_per_customer'],
        //             'customer_selection' => $updatedData['price_rule']['customer_selection'],
        //             'starts_at' => $startDate,
        //             'ends_at' => $endDate,
        //         ]
        //     ]
        // ]);

        // Profile update data into database
        DB::table('giveaways')->where('id', $request->id)->update([
            'title' => $request['title'],
            'prize_amount' => $request['prize_amount'],
            'image' => $imageName,
            'draw_on' => $drawTime,
            'winner' => ''
        ]);

        // return $response;
    }

    public function getCustomers($limit)
    {
        $response = $this->client->get("customers.json?limit={$limit}");
        $customers = json_decode($response->getBody()->getContents(), true)['customers'];
        echo "<pre>";
        print_r($customers);
        exit;
        return $products;
    }

    public function getAllCustomers()
    {
        $client = new Client([
            'base_uri' => "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/",
            'auth' => ["{$this->apiKey}", "{$this->password}"],
        ]);

        // Send GET request to Shopify's customers.json endpoint
        $response = $client->get('customers.json');

        // Decode JSON response to array
        $customersData = json_decode($response->getBody()->getContents(), true);
        echo "<pre>";
        print_r($customersData);
        echo "</pre>";
    }

    public function updateInventoryTracking()
    {
        $nextPageUrl = 'products.json?limit=250';

        do {
            $response = $this->client->get($nextPageUrl);
            $products = json_decode($response->getBody()->getContents(), true);
            // echo "<pre>"; print_r($products); echo "</pre>"; exit;

            foreach ($products['products'] as $product) {
                foreach ($product['variants'] as $variant) {
                    $variantId = $variant['id'];
                    // echo $variantId . "<br/>"; continue;

                    $updateData = [
                        'variant' => [
                            'id' => $variantId,
                            'inventory_management' => 'shopify',
                            'inventory_policy' => 'deny'
                        ]
                    ];

                    $response = $this->client->put(
                        "variants/{$variantId}.json",
                        [
                            'json' => $updateData
                        ]
                    );
                }
                Product::where('shopify_product_id', $product['id'])->update(['shopify_status' => 'Inventory Tracked']);
            }

            // Check if there's a next page
            // $headers = $this->client->getHeaders();
            $headers = $response->getHeaders();
            $nextPageUrl = null;

            if (isset($headers['Link'])) {
                $links = explode(',', $headers['Link'][0]);
                foreach ($links as $link) {
                    if (strpos($link, 'rel="next"') !== false) {
                        $nextPageUrl = trim(explode(';', $link)[0], '<>');
                        break;
                    }
                }
            }
        } while ($nextPageUrl);
    }


    function extractIdFromGid($gid)
    {
        $parts = explode('/', $gid);
        return end($parts);
    }

    public function getShopifyOrders()
    {
        $nextPageInfo = null;
        try {
            $orders = "orders.json";
            $customerIds = [];
            $customerRewards = [];

            do {
                $query = [
                    'limit' => 5,
                ];

                if ($nextPageInfo) {
                    $query['page_info'] = $nextPageInfo;
                }

                $response = $this->client->get($orders, [
                    'query' => $query
                ]);
                $order_response = json_decode($response->getBody()->getContents(), true);
                // echo "<pre>"; print_r($order_response); echo "</pre>"; exit;

                foreach ($order_response['orders'] as $order) {


                    $customerId = $order['customer']['id'];

                    // Initialize customer reward points if not already set
                    if (!isset($customerRewards[$customerId])) {
                        $customerRewards[$customerId] = 0;
                    }

                    // Check if note_attributes exist and loop through them to find reward points
                    if (isset($order['note_attributes']) && is_array($order['note_attributes'])) {
                        foreach ($order['note_attributes'] as $attribute) {
                            if (isset($attribute['name']) && $attribute['name'] === 'Total_Entries' && isset($attribute['value'])) {
                                $customerRewards[$customerId] += (int) $attribute['value']; // Accumulate total points
                            }
                        }
                    }
                    // echo "<pre>"; print_r($customerIds); continue;

                    $planName = $order['line_items'][0]['name'];

                    // Define plan type and received entries as per play name
                    $received_entries = 0;
                    $planType = '';
                    if ($planName == "Bronze") {
                        $planType = "One Time";
                        $received_entries = 1;
                    } else if ($planName == "Silver") {
                        $planType = "One Time";
                        $received_entries = 4;
                    } else if ($planName == "Gold") {
                        $planType = "One Time";
                        $received_entries = 10;
                    } else if ($planName == "Platinum") {
                        $planType = "One Time";
                        $received_entries = 30;
                    } else if ($planName == "Diamond") {
                        $planType = "One Time";
                        $received_entries = 100;
                    } else if ($planName == "Black Membership") {
                        $planType = "Monthly Subscription";
                        $received_entries = 1;
                    } else if ($planName == "Platinum Membership") {
                        $planType = "Monthly Subscription";
                        $received_entries = 20;
                    } else if ($planName == "Prestige Membership") {
                        $planType = "Monthly Subscription";
                        $received_entries = 90;
                    }
                    // echo $planType . " : " . $received_entries; //continue;

                    $dateTime = new \DateTime($order['created_at']);
                    $ordDate = $dateTime->format('Y-m-d');
                    //$orderDet = json_encode($order['line_items']);
                    $orderDet = json_encode($order);
                    // echo $orderDet; continue;

                    $shopify_order = DB::table('shopify_orders')->select('id', 'shopify_order_id')->where('shopify_order_id', $order['id'])->get()->toArray();
                    // echo "<pre>"; print_r($shopify_order); echo "</pre>"; continue;
                    if (count($shopify_order) > 0) {
                        // echo "Order updated : " . $order['id'] . "<br/>";
                        DB::table('shopify_orders')->where('shopify_order_id', $order['id'])
                            ->update([
                                'customer_id' => $customerId,
                                'plan' => $planName,
                                'plan_type' => $planType,
                                'received_entries' => $received_entries,
                                'order_detail' => $orderDet,
                                'order_date' => $ordDate,
                                'order_status' => 'Delivered'
                            ]);
                    } else {
                        // echo "Order added : " . $order['id'] . "<br/>";
                        DB::table('shopify_orders')->insert(
                            [
                                'shopify_order_id' => $order['id'],
                                'customer_id' => $customerId,
                                'plan' => $planName,
                                'plan_type' => $planType,
                                'received_entries' => $received_entries,
                                'order_detail' => $orderDet,
                                'order_date' => $ordDate,
                                'order_status' => 'Delivered'
                            ]
                        );
                    }

                    // update tags with customers
                    $tagString = $planName;
                    $updateData = [
                        'customer' => [
                            'id' => $customerId,
                            'tags' => $tagString
                        ]
                    ];
                    $updateResponse = $this->client->put("customers/{$customerId}.json", [
                        'json' => $updateData
                    ]);
                }
                $nextPageInfo = $this->extractNextPageInfo($response->getHeader('Link'));
                // exit;
            } while ($nextPageInfo);
        } catch (\Exception $e) {
            echo 'Unable to fetch order details.';
        }

        // update reward entries to metafields to customer
        foreach ($customerRewards as $customerId => $totalPoints) {
            // Shopify API endpoint
            $url = "https://{$this->storeName}.myshopify.com/admin/api/2023-10/customers/{$customerId}/metafields.json";

            // Metafield data
            $metafieldData = [
                'metafield' => [
                    'namespace' => 'custom',
                    'key' => 'total_entries',
                    'value' => (int) $totalPoints,
                    'type' => 'single_line_text_field'
                ]
            ];

            // Make API request to update metafield
            $response = Http::withHeaders([
                'X-Shopify-Access-Token' => $this->accessToken,
                'Content-Type' => 'application/json',
            ])->post($url, $metafieldData);

            // Handle API response
            if ($response->successful()) {
                echo "Updated customer {$customerId} with {$totalPoints} reward points.\n";
            } else {
                echo "Failed to update customer {$customerId}. Error: " . $response->body() . "\n";
            }
        }
    }

    //=============Order detail from shopify webhook=====================
    public function getShopifyOrdersWebhook(Request $request)
    {
        Log::info('Order detail from webhook: ', $request->all());

        $order_detail = $request;
        try {
            $shopifyOrderId = $order_detail['id'];
            $customerId = 0;
            $shopifyCustomerId = $order_detail['customer']['id'];

            // ============ Check if ordererd customer exist if not send invitation email================
            $checkMember = Customer::where('shopify_customer_id', $shopifyCustomerId)
                ->first();
            Log::info('Check membership detai    l : ');
            Log::info($checkMember);

            if (!$checkMember) { // If Guest User
                $response = Http::withHeaders([
                    'X-Shopify-Access-Token' => $this->accessToken,
                    'Content-Type' => 'application/json',
                ])->post("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/{$shopifyCustomerId}/send_invite.json");

                Log::info("Guest User : ");
                Log::info($response);

                if ($response->successful()) {
                    Log::info('Invitation sent to the cutsomer successfully...');
                } else {
                    Log::error('Failed to send Shopify account invite', [
                        'customer_id' => $shopifyCustomerId,
                        'response' => $response->body(),
                    ]);
                }

                Customer::create(
                    [
                        'shopify_customer_id' => $shopifyCustomerId,
                        'registered_from' => "Shopify",
                        'first_name' => $order_detail['customer']['first_name'],
                        'last_name' => $order_detail['customer']['last_name'],
                        'phone' => '',
                        'email' => $order_detail['customer']['email'],
                        'shopify_status' => 'Guest'
                    ]
                );
            }
            // ===================== End of invitation email send ============================== 

            $order_time = (new \DateTime($order_detail['created_at']))->setTimezone(new \DateTimeZone('UTC'))->format('Y-m-d H:i:s');
            $order_detail_json = json_encode($order_detail['line_items']);

            $order = Order::create([
                'shopify_order_id' => $shopifyOrderId,
                'customer_id' => $customerId,
                'shopify_customer_id' => $shopifyCustomerId,
                'order_detail' => $order_detail_json,
                'order_time' => $order_time,
                'order_status' => "Pending",
                'discount_code' => "",
                'discounted_amount' => $order_detail['total_discounts'],
                'payment_status' => "Pending",
                'transaction_id' => 0
            ]);
            $orderId = $order->id;

            $rewardPlans = ['Bronze', 'Gold', 'Diamond', 'Platinum', 'Silver'];
            $membershipPlans = ['Black Membership', 'Platinum Membership', 'Prestige Membership'];

            $order_total_entries = 0;
            $planNames = [];
            $new_membership = null;
            $new_membership_from = null;
            $new_membership_to = null;
            $new_membership_status = null;
            $new_subscription_count = 0;

            foreach ($order_detail['line_items'] as $lineitem) {
                $lineItemArray = [$lineitem];
                $order_item_json = json_encode($lineItemArray);

                $plan_type = 'Shop Product';
                $plan_entries = 0;
                $acm_entries = 0;
                $plan_amount = 0;
                $membershipStatus = '';

                if (in_array($lineitem['name'], $rewardPlans)) {
                    $plan_type = 'Reward Plan';
                    $planNames[] = trim($lineitem['name']);
                } elseif (in_array($lineitem['name'], $membershipPlans)) {
                    $plan_type = 'Membership Plan';
                    $planNames[] = trim($lineitem['name']);
                    $new_subscription_count++;
                }

                foreach ($lineitem['properties'] as $properties) {
                    if ($properties['name'] == 'entries') {
                        $plan_entries = $properties['value'];
                        $order_total_entries += ($properties['value'] * $lineitem['quantity']);
                    } elseif ($properties['name'] == '_accumulative_entries') {
                        $acm_entries = $properties['value'];
                    } elseif ($properties['name'] == '_plan_amount') {
                        $plan_amount = $properties['value'];
                    }
                }

                $orderItem = OrderItems::create([
                    'order_id' => $orderId,
                    'shopify_order_id' => $shopifyOrderId,
                    'customer_id' => $customerId,
                    'shopify_customer_id' => $shopifyCustomerId,
                    'product_type' => $plan_type,
                    'plan_name' => $lineitem['title'],
                    'plan_type' => $plan_type,
                    'plan_entries' => $plan_entries,
                    'plan_acm_entries' => $acm_entries,
                    'price' => ($lineitem['price'] > 0) ? $lineitem['price'] : $plan_amount,
                    'quantity' => $lineitem['quantity'],
                    'order_detail' => $order_item_json,
                    'order_time' => $order_time,
                    'order_status' => 'Pending',
                    'order_amount' => $lineitem['price'],
                    'payment_status' => "Pending",
                    'transaction_id' => 0
                ]);
                $orderDetId = $orderItem->id;

                // If any membership plan in order
                if ($plan_type == "Membership Plan") {
                    $existingMembership = Membership::where('shopify_customer_id', $shopifyCustomerId)
                        ->whereIn('membership_status', ['Active', 'Upcoming', 'Canceled']) // need to check with canceled
                        ->orderBy('membership_to', 'desc')
                        ->first();

                    if ($existingMembership && $existingMembership->membership_status == 'Canceled' && Carbon::parse($existingMembership->membership_from)->isFuture()) { // If membership canceled & future date
                        // Update the future canceled membership
                        $existingMembership->update([
                            'order_id' => $orderId,
                            'order_detail_id' => $orderDetId,
                            'plan_name' => $lineitem['name'],
                            'applicable_price' => ($lineitem['price'] > 0) ? $lineitem['price'] : $plan_amount,
                            'applicable_entries' => $plan_entries,
                            'applicable_acm_entries' => $acm_entries,
                            'membership_status' => 'Upcoming',
                            'payment_status' => 'Pending',
                            'transaction_id' => 0
                        ]);

                        $new_membership = $lineitem['name'];
                        $new_membership_from = Carbon::parse($existingMembership->membership_from)->format('Y-m-d H:i:s');
                        $new_membership_to = Carbon::parse($existingMembership->membership_to)->format('Y-m-d H:i:s');
                        $new_membership_status = 'Upcoming';
                        $membershipStatus = "Upcoming";
                    } elseif (!$existingMembership or ($existingMembership && $existingMembership->membership_status == 'Active') or ($existingMembership && $existingMembership->membership_status == 'Canceled')) {

                        if (!$existingMembership or ($existingMembership && $existingMembership->membership_status == 'Canceled')) {
                            $baseDate = Carbon::parse($order_time);
                            $new_membership_status = 'Active';
                            $membershipStatus = "Active";

                            // --------- If current/active membership is cancelled ------------
                            // if($existingMembership && $existingMembership->membership_status == 'Canceled'){
                            //     $canceled_membership_from = Carbon::parse($existingMembership->membership_from)->format('Y-m-d H:i:s');
                            //     $canceled_membership_to = Carbon::parse($existingMembership->membership_to)->format('Y-m-d H:i:s');

                            //     $cancelToActive = false;
                            //     if (Carbon::parse($canceled_membership_from)->diffInHours($baseDate, false) < 24 && $baseDate->greaterThan($canceled_membership_from)) {
                            //         $cancelToActive = true;
                            //     }else{
                            //         $cancelToActive = false;
                            //     }


                            // // Check for make payment here if required

                            // }
                            // ----End ---------If membership exists but cancelled -----


                        } elseif ($existingMembership && $existingMembership->membership_status == 'Active') {
                            $baseDate = Carbon::parse($existingMembership->membership_to);
                            $new_membership_status = 'Upcoming';
                            $membershipStatus = "Upcoming";
                        }

                        $fromDate = $baseDate->copy();
                        $toDate = $baseDate->copy()->addMonth();

                        Membership::create([
                            'order_id' => $orderId,
                            'shopify_order_id' => $shopifyOrderId,
                            'customer_id' => $customerId,
                            'shopify_customer_id' => $shopifyCustomerId,
                            'order_detail_id' => $orderDetId,
                            'product_type' => $plan_type,
                            'plan_name' => $lineitem['name'],
                            'membership_from' => $fromDate,
                            'membership_to' => $toDate,
                            'applicable_price' => ($lineitem['price'] > 0) ? $lineitem['price'] : $plan_amount,
                            'applicable_entries' => $plan_entries,
                            'applicable_acm_entries' => $acm_entries,
                            'membership_status' => $new_membership_status,
                            'payment_status' => 'Pending',
                            'transaction_id' => 0
                        ]);
                        $new_membership = $lineitem['name'];
                        $new_membership_from = $fromDate->format('Y-m-d');
                        $new_membership_to = $toDate->format('Y-m-d');
                    }
                }
            }

            Order::where('id', $orderId)->update([
                'order_total_entries' => $order_total_entries,
                'order_amount' => $order_detail['total_price']
            ]);

            // === Customer metafield update ===
            $customerDetails = $this->getCustomerDetById($shopifyCustomerId);
            $existing_tags = preg_split('/\s*,\s*/', $customerDetails['customer']['tags']);
            $allTagsArray = array_unique(array_merge($existing_tags, $planNames));

            $this->client->put("customers/{$shopifyCustomerId}.json", [
                'json' => ['customer' => ['id' => $shopifyCustomerId, 'tags' => implode(",", $allTagsArray)]]
            ]);

            // === Metafields ===
            $customerMetafields = $this->getCustomerMetafields($shopifyCustomerId);
            $metafieldMap = collect($customerMetafields['metafields'])->pluck('value', 'key')->toArray();

            $total_entries = isset($metafieldMap['total_entries']) ? $metafieldMap['total_entries'] : 0;
            $total_subscriptions = isset($metafieldMap['total_monthly_subscriptions']) ? $metafieldMap['total_monthly_subscriptions'] : 0;

            if ($new_membership_status != 'Upcoming') {
                $metafields = [
                    [
                        'namespace' => 'custom',
                        'key' => 'total_entries',
                        'value' => (int) $total_entries + $order_total_entries,
                        'type' => 'number_integer'
                    ],
                    [
                        'namespace' => 'custom',
                        'key' => 'total_monthly_subscriptions',
                        'value' => (int) $total_subscriptions + $new_subscription_count,
                        'type' => 'number_integer'
                    ]
                ];
            }

            if ($new_membership) {
                if ($new_membership_status == 'Active') {
                    $metafields[] = ['namespace' => 'custom', 'key' => 'active_membership', 'value' => $new_membership, 'type' => 'single_line_text_field'];
                    $metafields[] = ['namespace' => 'custom', 'key' => 'membership_from', 'value' => $new_membership_from, 'type' => 'single_line_text_field'];
                    $metafields[] = ['namespace' => 'custom', 'key' => 'membership_to', 'value' => $new_membership_to, 'type' => 'single_line_text_field'];
                } else {
                    $metafields[] = ['namespace' => 'custom', 'key' => 'upcoming_membership', 'value' => $new_membership, 'type' => 'single_line_text_field'];
                }
            }

            Log::info("=====Metafields=========");
            Log::info($metafields);

            $url = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/{$shopifyCustomerId}/metafields.json";
            foreach ($metafields as $meta) {
                Http::withHeaders([
                    'X-Shopify-Access-Token' => $this->accessToken,
                    'Content-Type' => 'application/json',
                ])->post($url, ['metafield' => $meta]);
            }

            // ------- Send link to setup stripe payment for both Registered/Gues ----------
            // $custDet = Customer::where('shopify_customer_id', $shopifyCustomerId)->first()->toArray();

            $custModel = Customer::where('shopify_customer_id', $shopifyCustomerId)->first();
            $custDet = $custModel ? [$custModel->toArray()] : [];

            Log::info('Customer Detail for Stripe : ');
            Log::info($custDet);
            if ($custDet && $custDet[0]['stripe_customer_id'] == '') {
                $this->createCustomerByEmail($shopifyCustomerId, $custDet[0]['email'], $custDet[0]['first_name'] . " " . $custDet[0]['last_name']);
            }
            // Log::info('Stripe response for customer created : ');
            // Log::info($stripeCustomer);
            // --------------End of send link to setup stripe payment------

            Log::info("Order processed and metafields updated successfully.");
            return response()->json(['message' => 'Order processed successfully'], 200);
        } catch (\Exception $e) {
            Log::error('Error in getShopifyOrdersWebhook: ' . $e->getMessage());
            return response()->json(['message' => 'Error processing order'], 500);
        }
    }

    public function createDraftOrder($request)
    {
        // Log::info($request);

        $lineItems = $request->input('line_items');  // Get line items from the frontend request
        Log::info("Line Items : ");
        Log::info($lineItems);

        // Make the request to Shopify Admin API to create a draft order
        $draftorder_response = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->accessToken,
            'Content-Type' => 'application/json',
        ])->post("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/draft_orders.json", [
            'draft_order' => [
                'line_items' => [
                    [
                        'variant_id' => $lineItems[0]['variant_id'],
                        'quantity' => 1,
                        'properties' => [
                            [
                                'name' => 'plan_type',
                                'value' => 'Monthly Subscription',
                            ],
                            [
                                'name' => 'entries',
                                'value' => $lineItems[0]['entries'],
                            ],
                            [
                                'name' => '_accumulative_entries',
                                'value' => $lineItems[0]['accumulative_entries'],
                            ],
                            [
                                'name' => '_customer_subscription_no',
                                'value' => $lineItems[0]['subscription_no'],
                            ],
                            [
                                'name' => '_plan_amount',
                                'value' => $lineItems[0]['plan_amount'],
                            ]
                        ]
                    ]
                ],
                'note' => 'Created Draft Order',
                'use_customer_default_address' => true,
            ],
        ]);

        $draftOrderData = $draftorder_response->json();

        Log::info($draftOrderData);

        if (isset($draftOrderData['draft_order'])) {
            Log::info($draftOrderData['draft_order']);

            $draftOrder = $draftOrderData['draft_order'];

            // Now you can access details safely
            $draftOrderId = $draftOrder['id'] ?? null;
            $invoiceUrl = $draftOrder['invoice_url'] ?? null;
        } else {
            Log::info("Draft order creation failed or wrong response.");
        }

        // ========== Update price of draft order ids =====================
        // Line item adjustments to set price to zero
        $updatedLineItems = [
            [
                'id' => $draftOrder['line_items'][0]['id'], // Required for updating an existing line item
                'title' => $draftOrder['line_items'][0]['title'],
                'price' => '0.00',
                'quantity' => 1,
                'custom' => true,
                'properties' => [
                    [
                        'name' => 'plan_type',
                        'value' => 'Monthly Subscription',
                    ],
                    [
                        'name' => 'entries',
                        'value' => $lineItems[0]['entries'],
                    ],
                    [
                        'name' => '_accumulative_entries',
                        'value' => $lineItems[0]['accumulative_entries'],
                    ],
                    [
                        'name' => '_customer_subscription_no',
                        'value' => $lineItems[0]['subscription_no'],
                    ],
                    [
                        'name' => '_plan_amount',
                        'value' => $lineItems[0]['plan_amount'],
                    ]
                ]
            ]
        ];

        $response_update = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->accessToken,
            'Content-Type' => 'application/json',
        ])->put("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/draft_orders/{$draftOrderId}.json", [
            'draft_order' => [
                'line_items' => $updatedLineItems,
            ],
        ]);

        $updatedDraftOrder = $response_update->json();

        Log::info("Invoice URL after updated draft order : " . $updatedDraftOrder['draft_order']['invoice_url']);
        Log::info("Updated draft order detail :");
        Log::info($updatedDraftOrder);

        return $updatedDraftOrder;
    }

    public function cronUpdateMembership()
    {
        Log::info('===== Cron Job Started: Update Membership Statuses =====');

        // Get customer ids who has Active/Upcoming status of last mebeership
        $customerIds = Membership::whereIn('membership_status', ['Active', 'Upcoming', 'Canceled'])
            ->pluck('shopify_customer_id')
            ->unique()
            ->values()
            ->toArray();

        // echo "<pre>"; print_r($customerIds);
        $today = Carbon::now()->startOfDay();
        $currentDateTime = Carbon::now();
        Log::info("Current Date & Time : " . $currentDateTime);

        foreach ($customerIds as $shopifyCustomerId) {
            // Get csutomer details with latest membership
            $customerMemberships = Membership::where('shopify_customer_id', $shopifyCustomerId)
                ->orderBy('membership_to', 'desc')
                ->get();

            $canceledMembership = $customerMemberships->first(function ($membership) use ($currentDateTime) {
                return $membership->membership_status === 'Canceled';
            });

            if (!$canceledMembership) {
                $expiredMembership = $customerMemberships->first(function ($membership) use ($currentDateTime) {
                    return $membership->membership_status === 'Active' && Carbon::parse($membership->membership_to)->lt($currentDateTime);
                });

                Log::info("Expired Membership");
                Log::info($expiredMembership);
                Log::info("-------------------------------------------------------");

                if ($expiredMembership) {
                    // Expire the current active membership
                    $expiredMembership->membership_status = 'Expired';
                    $expiredMembership->save();

                    Log::info("Expired membership ID {$expiredMembership->id} for customer {$shopifyCustomerId}");

                    // Calculate new dates (+1 month)
                    $previousFrom = Carbon::parse($expiredMembership->membership_from)->addMonth();
                    $previousTo = Carbon::parse($expiredMembership->membership_to)->addMonth();

                    // Check for upcoming membership
                    $upcomingMembership = $customerMemberships->first(function ($membership) {
                        return $membership->membership_status === 'Upcoming';
                    });

                    // echo "<pre>"; print_r($upcomingMembership); echo "</pre>";
                    Log::info("Upcoming Membership");
                    Log::info($upcomingMembership);
                    Log::info("-------------------------------------------------------");
                    if ($upcomingMembership) {
                        $shopifyCustomerId;
                        // Activate the upcoming membership
                        $planChanged = $upcomingMembership->plan_name !== $expiredMembership->plan_name;

                        $upcomingMembership->membership_status = 'Active';
                        $upcomingMembership->membership_from = $previousFrom;
                        $upcomingMembership->membership_to = $previousTo;


                        Log::info("Activated upcoming membership ID {$upcomingMembership->id} for customer {$shopifyCustomerId}");

                        // -------- Make Payement for upcoming membership --------
                        if ($upcomingMembership->applicable_discount == 50) {
                            $payble_amount = $upcomingMembership->applicable_price / 2;
                        } else {
                            $payble_amount = $upcomingMembership->applicable_price;
                        }
                        Log::info('Payble Amount : ' . $payble_amount);
                        $custDet = Customer::where('shopify_customer_id', $shopifyCustomerId)->get()->toArray();
                        // echo "<pre>"; print_r($custDet); echo "</pre>"; continue;
                        $paymentInfo = $this->makePayment($custDet[0]['stripe_customer_id'], $payble_amount, $custDet[0]['payment_method_id']);
                        // --------End --- upcoming membership payment ------------
                        if ($paymentInfo['status'] == 'succeeded') {
                            $upcomingMembership->payment_status = 'Paid';
                            $upcomingMembership->transaction_id = $paymentInfo['id'];
                        } else {
                            $upcomingMembership->payment_status = 'Fail' . $paymentInfo['cancellation_reason'];
                            $upcomingMembership->transaction_id = $paymentInfo['id'];
                        }
                        $upcomingMembership->save();

                        $this->updateShopifyMetafields(
                            $shopifyCustomerId,
                            $upcomingMembership->plan_name,
                            $upcomingMembership->membership_from,
                            $upcomingMembership->membership_to,
                            $upcomingMembership->applicable_entries,
                            $upcomingMembership->applicable_acm_entries,
                            $isRecurring = true,
                            $planChanged
                        );
                    } else {
                        // No upcoming membership → create recurring from expired one
                        $newMembership = $expiredMembership->replicate();
                        $newMembership->membership_status = 'Active';
                        $newMembership->membership_from = $previousFrom;
                        $newMembership->membership_to = $previousTo;
                        $newMembership->applicable_discount = 0;
                        $newMembership->created_at = now();
                        $newMembership->updated_at = now();
                        $newMembership->save();

                        Log::info("Created recurring membership ID {$newMembership->id} for customer {$shopifyCustomerId}");

                        $this->updateShopifyMetafields(
                            $shopifyCustomerId,
                            $newMembership->plan_name,
                            $newMembership->membership_from,
                            $newMembership->membership_to,
                            $newMembership->applicable_entries,
                            $newMembership->applicable_acm_entries,
                            $isRecurring = true,
                            $planChanged = false
                        );
                    }

                    // =========== Check & Update bonus entries if any ===============
                    $customerBonus = BonusEntries::where('shopify_customer_id', $shopifyCustomerId)
                        ->where('bonus_applicable_on', '!=', '')
                        ->where('bonus_status', 'pending')
                        ->orderBy('received_bonus_on', 'desc')
                        ->first()?->toarray();

                    // echo "<pre>"; print_r($customerBonus); echo "</pre>"; continue;

                    if ($customerBonus) {
                        $dateString = $previousFrom;
                        $date = new \DateTime($dateString);
                        $formattedDate = $date->format('F Y'); // 'F' = full month name, 'Y' = 4-digit year
                        Log::info("Bonus Applicable On : " . $formattedDate . " : " . $customerBonus['bonus_applicable_on']);

                        if ($formattedDate == $customerBonus['bonus_applicable_on']) {
                            $bonus_status = "Used";
                            $this->incrementCustomerTotalEntries($shopifyCustomerId, $customerBonus['received_bonus']);
                        }

                        $updated = BonusEntries::updateOrCreate(
                            ['shopify_customer_id' => $shopifyCustomerId],
                            [
                                'bonus_status' => $bonus_status
                            ]
                        );
                    }
                    // ==== End - Check & Update bonus entries if any =================
                }
                // ================Update Membership=====================
                // cronUpdateBonusPoints($shopifyCustomerId);
            }
        }
        Log::info('===== Cron Job Completed: Update Membership Statuses =====');
        return 0;
    }

    public function continueMembershipOffer($request)
    {
        $response_membership = '';
        Log::info('===== Membership offer continue with 50%=====');
        $shopifyCustomerId = $request->customer_id;
        Log::info("Shopify Customer Id : " . $shopifyCustomerId);
        // Get customer ids who has Active/Upcoming status of last mebeership
        $customerMemberships = Membership::where('shopify_customer_id', $shopifyCustomerId)
            ->orderBy('membership_to', 'desc')
            ->get();

        // Get first membership where status is Active or Upcoming
        $lastMembership = $customerMemberships->first(function ($membership) {
            return in_array($membership->membership_status, ['Active', 'Upcoming']);
        });

        Log::info("Last Membership : ");
        Log::info($lastMembership);

        $metafields = [];

        if ($lastMembership) {

            if ($lastMembership->membership_status === 'Active') {
                // Create new Upcoming membership

                $offerMembership = $lastMembership->replicate();

                // Increment dates by 1 month
                $previousFrom = Carbon::parse($lastMembership->membership_from)->addMonth();
                $previousTo = Carbon::parse($lastMembership->membership_to)->addMonth();

                $offerMembership->applicable_discount = 50;
                $offerMembership->membership_status = 'Upcoming';
                $offerMembership->membership_from = $previousFrom->toDateString();
                $offerMembership->membership_to = $previousTo->toDateString();
                $offerMembership->created_at = now();
                $offerMembership->updated_at = now();
                $offerMembership->save();

                $response_membership = response()->json([
                    'status' => true,
                    'message' => 'New upcoming membership created with 50% discount.'
                ]);

                $metafields = [
                    [
                        'namespace' => 'custom',
                        'key' => 'upcoming_membership',
                        'value' => $offerMembership->plan_name,
                        'type' => 'single_line_text_field',
                    ]
                ];
            }

            if ($lastMembership->membership_status === 'Upcoming') {
                // Update existing Upcoming membership with discount

                $lastMembership->applicable_discount = 50;
                $lastMembership->updated_at = now();
                $lastMembership->save();

                $response_membership = response()->json([
                    'status' => true,
                    'message' => 'Existing upcoming membership updated with 50% discount.'
                ]);

                $metafields = [
                    [
                        'namespace' => 'custom',
                        'key' => 'upcoming_membership',
                        'value' => $lastMembership->plan_name,
                        'type' => 'single_line_text_field',
                    ]
                ];
            }

            // ================ update metafields for upcoming membership ============
            $metafieldsUrl = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/{$shopifyCustomerId}/metafields.json";

            $response = Http::withHeaders([
                'X-Shopify-Access-Token' => $this->accessToken,
            ])->get($metafieldsUrl);

            $existingMetafields = collect($response->json('metafields'));
            Log::info('Existing Metafields');
            Log::info($existingMetafields);

            foreach ($metafields as $metafieldData) {

                $payload = ['metafield' => $metafieldData];

                $response = Http::withHeaders([
                    'X-Shopify-Access-Token' => $this->accessToken,
                    'Content-Type' => 'application/json',
                ])->post($metafieldsUrl, $payload);

                Log::info('Metafield Updated Response', [
                    'payload' => $payload,
                    'response' => $response->body(),
                ]);
                Log::info('-------------------------------------------------------------');
            }
            // ============ End of update metafields for upcoming membership =========
        }

        if ($response_membership == '') {
            $response_membership =  response()->json([
                'status' => false,
                'message' => 'No active or upcoming membership found.',
            ]);
        }
        return $response_membership;
    }

    public function cancelMembership($request)
    {
        Log::info('===== Cancel Membership =====');
        $shopifyCustomerId = $request->customer_id;
        Log::info("Shopify Customer Id : " . $shopifyCustomerId);
        // Get customer ids who has Active/Upcoming status of last mebeership
        $customerMemberships = Membership::where('shopify_customer_id', $shopifyCustomerId)
            ->orderBy('membership_to', 'desc')
            ->get();

        // Get first membership where status is Active or Upcoming
        $lastMembership = $customerMemberships->first(function ($membership) {
            return in_array($membership->membership_status, ['Active', 'Upcoming', 'Canceled']);
        });

        Log::info('Last membership detail');
        Log::info($lastMembership);
        $response_cancel = '';
        if ($lastMembership) {
            if ($lastMembership->membership_status === 'Active') {
                // Create new Upcoming membership

                $offerMembership = $lastMembership->replicate();

                // Increment dates by 1 month
                $previousFrom = Carbon::parse($lastMembership->membership_from)->addMonth();
                $previousTo = Carbon::parse($lastMembership->membership_to)->addMonth();
                $offerMembership->membership_status = 'Canceled';
                $offerMembership->membership_from = $previousFrom->toDateString();
                $offerMembership->membership_to = $previousTo->toDateString();
                $offerMembership->created_at = now();
                $offerMembership->updated_at = now();

                $offerMembership->save();

                $response_cancel = response()->json([
                    'status' => true,
                    'message' => 'Membership Canceled.'
                ]);
            } elseif ($lastMembership->membership_status === 'Upcoming') {
                // Update existing Upcoming membership with discount

                $lastMembership->membership_status = 'Canceled';
                $lastMembership->updated_at = now();
                $lastMembership->save();

                $response_cancel = response()->json([
                    'status' => true,
                    'message' => 'Existing upcoming membership cancelled.'
                ]);
            } elseif ($lastMembership->membership_status === 'Canceled') {
                $response_cancel = response()->json([
                    'status' => true,
                    'message' => 'Membership already cancelled.'
                ]);
            }

            // update metafields as cancelled
            $metafieldsUrl = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/{$shopifyCustomerId}/metafields.json";

            $metafields = [
                [
                    'namespace' => 'custom',
                    'key' => 'upcoming_membership',
                    'value' => "Canceled",
                    'type' => 'single_line_text_field',
                ]
            ];

            foreach ($metafields as $metafieldData) {

                $payload = ['metafield' => $metafieldData];

                // Log::info('Matafields Payload');
                // Log::info($payload);

                $response = Http::withHeaders([
                    'X-Shopify-Access-Token' => $this->accessToken,
                    'Content-Type' => 'application/json',
                ])->post($metafieldsUrl, $payload);

                Log::info('Metafield Updated Response', [
                    'payload' => $payload,
                    'response' => $response->body(),
                ]);
                Log::info('-------------------------------------------------------------');
            }
        }

        if ($response_cancel == '') {
            $response_cancel = response()->json([
                'status' => false,
                'message' => 'No active or upcoming membership found.',
            ]);
        }
        return $response_cancel;
    }

    function cronUpdateBonusPoints()
    {
        $customerIds = Membership::whereIn('membership_status', ['Expired', 'Active', 'Upcoming'])
            ->pluck('shopify_customer_id')
            ->unique()
            ->values()
            ->toArray();

        Log::info('Processing customer IDs:', $customerIds);

        // Define milestone bonus structure
        $bonusSchedule = [
            3 => 5,
            6 => 10,
            9 => 10,
            12 => 10,
            18 => 10,
            24 => 10,
            30 => 10,
            36 => 10,
            42 => 10,
            48 => 10,
            50 => 10,
        ];

        foreach ($customerIds as $shopifyCustomerId) {
            $memberships = Membership::where('shopify_customer_id', $shopifyCustomerId)
                ->orderBy('membership_from')
                ->get();

            if ($memberships->isEmpty()) continue;

            $validMemberships = $memberships->filter(function ($m) {
                return Carbon::parse($m->membership_from)->lessThanOrEqualTo(Carbon::now());
            });

            if ($validMemberships->isEmpty()) continue;

            // Build continuous periods with 24-hour max gap
            $continuousPeriods = [];
            $currentStart = null;
            $currentEnd = null;

            foreach ($validMemberships as $membership) {
                $from = Carbon::parse($membership->membership_from);
                $to = Carbon::parse($membership->membership_to);

                if (is_null($currentStart)) {
                    $currentStart = $from;
                    $currentEnd = $to;
                } else {
                    $gapHours = $currentEnd->diffInHours($from, false); // Negative if overlapping
                    if ($gapHours >= 0 && $gapHours <= 24) {
                        $currentEnd = $to->greaterThan($currentEnd) ? $to : $currentEnd;
                    } elseif ($from->lessThanOrEqualTo($currentEnd)) {
                        $currentEnd = $to->greaterThan($currentEnd) ? $to : $currentEnd;
                    } else {
                        $continuousPeriods[] = ['start' => $currentStart, 'end' => $currentEnd];
                        $currentStart = $from;
                        $currentEnd = $to;
                    }
                }
            }

            if (!is_null($currentStart)) {
                $continuousPeriods[] = ['start' => $currentStart, 'end' => $currentEnd];
            }

            // Only proceed if latest status is Active or Upcoming
            $latestStatus = $memberships->last()->membership_status;
            if (!in_array($latestStatus, ['Active', 'Upcoming'])) continue;

            // Calculate bonus points per period
            $totalBonus = 0;
            foreach ($continuousPeriods as $period) {
                $start = $period['start'];
                $end = $period['end'];
                $effectiveEnd = $end->lessThan(Carbon::now()) ? $end : Carbon::now();

                $months = $start->diffInMonths($effectiveEnd);
                $periodBonus = 0;

                foreach ($bonusSchedule as $milestone => $points) {
                    if ($months >= $milestone) {
                        $periodBonus += $points;
                    }
                }

                $totalBonus += $periodBonus;
            }

            // Deduct already used bonuses
            $totalUsedBonus = BonusEntries::where('shopify_customer_id', $shopifyCustomerId)
                ->where('bonus_status', 'Used')
                ->sum('received_bonus');

            $availableBonus = $totalBonus - $totalUsedBonus;
            if ($availableBonus < 0) $availableBonus = 0;

            $bonusEntry = BonusEntries::where('shopify_customer_id', $shopifyCustomerId)
                ->where('bonus_status', 'Added')
                ->first();

            $membershipSince = Carbon::parse($validMemberships->first()->membership_from)->toDateString();

            if ($availableBonus > 0) {
                if (!$bonusEntry) {
                    BonusEntries::create([
                        'shopify_customer_id' => $shopifyCustomerId,
                        'customer_id'         => '',
                        'membership_since'    => $membershipSince,
                        'received_bonus'      => $availableBonus,
                        'received_bonus_on'   => now()->toDateTimeString(),
                        'bonus_applicable_on' => '',
                        'bonus_status'        => 'Added',
                    ]);
                    Log::info("Bonus created | Customer ID: $shopifyCustomerId | Points: $availableBonus");
                } else {
                    $bonusEntry->update([
                        'received_bonus'    => $availableBonus,
                        'received_bonus_on' => now()->toDateTimeString(),
                        'membership_since'  => $membershipSince,
                    ]);
                    Log::info("Bonus updated | Customer ID: $shopifyCustomerId | Points: $availableBonus");
                }
            }
        }

        return 'Bonus update cron executed successfully.';
    }

    function cronUpdateRewardPoints()
    {

        $currentDateTime = Carbon::now();
        $firstOfMonth = Carbon::now()->startOfMonth();

        if ($currentDateTime->greaterThan($firstOfMonth)) {
            // Current date is after the first date of the month
            // Your logic here

            $customerIds = Membership::whereIn('membership_status', ['Expired', 'Active', 'Upcoming'])
                ->pluck('shopify_customer_id')
                ->unique()
                ->values()
                ->toArray();

            Log::info('Processing customer IDs:', $customerIds);

            foreach ($customerIds as $shopifyCustomerId) {
                // $memberships = Membership::where('shopify_customer_id', $shopifyCustomerId)
                //     ->orderBy('membership_from')
                //     ->get();

                $metafieldsUrl = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/{$shopifyCustomerId}/metafields.json";

                $metafields = [];
                if ($shopifyCustomerId == "7859379863599") {
                    $metafields = [
                        [
                            'namespace' => 'custom',
                            'key' => 'total_entries',
                            'value' => 0,
                            'type' => 'number_integer',
                        ],
                    ];

                    foreach ($metafields as $metafieldData) {
                        $payload = ['metafield' => $metafieldData];
                        $response = Http::withHeaders([
                            'X-Shopify-Access-Token' => $this->accessToken,
                            'Content-Type' => 'application/json',
                        ])->post($metafieldsUrl, $payload);

                        Log::info('Metafield Updated Response', [
                            'payload' => $payload,
                            'response' => $response->body(),
                        ]);
                        Log::info('-------------------------------------------------------------');
                    }
                }
            }
        }
        return 'Reward points - update cron executed successfully.';
    }

    public function updateShopifyMetafields($shopifyCustomerId, $activeMembershipName, $membershipFrom, $membershipTo, $applicableEntries, $acmEntries, $isRecurring, $planChanged)
    {
        $membershipFromFormatted = Carbon::parse($membershipFrom)->format('Y-m-d H:i:s');
        $membershipToFormatted = Carbon::parse($membershipTo)->format('Y-m-d H:i:s');

        $metafieldsUrl = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/{$shopifyCustomerId}/metafields.json";

        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->accessToken,
        ])->get($metafieldsUrl);

        $existingMetafields = collect($response->json('metafields'));

        $totalFreeEntries = (int) ($existingMetafields->firstWhere('key', 'total_entries')['value'] ?? 0);
        $totalSubscriptions = (int) ($existingMetafields->firstWhere('key', 'total_monthly_subscriptions')['value'] ?? 0);

        // Only add entries/subscriptions if it is recurring
        $additionalFreeEntries = $isRecurring ? ($applicableEntries + ($acmEntries * $totalSubscriptions)) : 0;
        $updatedTotalFreeEntries = $totalFreeEntries + $additionalFreeEntries;
        $updatedTotalSubscriptions = $isRecurring ? ($totalSubscriptions + 1) : $totalSubscriptions;

        $metafields = [
            [
                'namespace' => 'custom',
                'key' => 'upcoming_membership',
                'value' => "-",
                'type' => 'single_line_text_field',
            ],
            [
                'namespace' => 'custom',
                'key' => 'active_membership',
                'value' => $activeMembershipName,
                'type' => 'single_line_text_field',
            ],
            [
                'namespace' => 'custom',
                'key' => 'membership_from',
                'value' => $membershipFromFormatted,
                'type' => 'single_line_text_field',
            ],
            [
                'namespace' => 'custom',
                'key' => 'membership_to',
                'value' => $membershipToFormatted,
                'type' => 'single_line_text_field',
            ],
            [
                'namespace' => 'custom',
                'key' => 'total_entries',
                'value' => $updatedTotalFreeEntries,
                'type' => 'number_integer',
            ],
            [
                'namespace' => 'custom',
                'key' => 'total_monthly_subscriptions',
                'value' => $updatedTotalSubscriptions,
                'type' => 'number_integer',
            ],
        ];

        // Log::info("Upcoming Membership");
        // Log::info($metafields);
        // Log::info("-------------------------------------------------------");

        foreach ($metafields as $metafieldData) {

            $payload = ['metafield' => $metafieldData];

            // Log::info('Matafields Payload');
            // Log::info($payload);

            $response = Http::withHeaders([
                'X-Shopify-Access-Token' => $this->accessToken,
                'Content-Type' => 'application/json',
            ])->post($metafieldsUrl, $payload);

            Log::info('Metafield Updated Response', [
                'payload' => $payload,
                'response' => $response->body(),
            ]);
            Log::info('-------------------------------------------------------------');
        }

        // Update Customer Tags if Plan Changed
        if ($planChanged) {
            $customerDetails = $this->getCustomerDetById($shopifyCustomerId);
            $existingTags = array_filter(array_map('trim', explode(',', $customerDetails['customer']['tags'])));

            if (!in_array($activeMembershipName, $existingTags)) {
                $existingTags[] = $activeMembershipName;
            }

            $allTags = implode(', ', $existingTags);

            $updateData = [
                'customer' => [
                    'id' => $shopifyCustomerId,
                    'tags' => $allTags
                ],
            ];

            $updateResponse = Http::withHeaders([
                'X-Shopify-Access-Token' => $this->accessToken,
                'Content-Type' => 'application/json',
            ])->put("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/{$shopifyCustomerId}.json", $updateData);

            Log::info('Updated Customer Tags', [
                'tags' => $allTags,
                'response' => $updateResponse->body(),
            ]);
        }
    }

    function getCustomerDetById($customerId)
    {
        $shopifyStore = env('SHOPIFY_STORE_URL'); // e.g., 'your-store.myshopify.com'
        $accessToken = env('SHOPIFY_ACCESS_TOKEN'); // Shopify API access token

        $url = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/$customerId.json";

        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->accessToken,
            'Content-Type' => 'application/json',
        ])->get($url);

        if ($response->successful()) {
            return $response->json();
        } else {
            return $response->body(); // Returns error details if request fails
        }
    }

    function getCustomerMetafields($customerId)
    {
        // $customerResponse = Http::withHeaders([
        //     'X-Shopify-Access-Token' => $this->accessToken,
        // ])->get("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/$customerId.json");
        // $customer = $customerResponse->json();

        $metafieldsResponse = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->accessToken,
        ])->get("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/$customerId/metafields.json");

        $metafields = $metafieldsResponse->json();

        Log::info($metafields);

        return $metafields;
    }

    public function getBonusPoints($shopifyCustomerId)
    {
        $bonus = BonusEntries::where('shopify_customer_id', $shopifyCustomerId)->where('bonus_status', '!=', 'Used')->first();
        // echo "<pre>"; print_r($bonus); echo "</pre>"; exit;

        return response()->json([
            'shopify_customer_id' => $shopifyCustomerId,
            'received_bonus'      => $bonus ? $bonus->received_bonus : 0,
            'bonus_status'        => $bonus ? $bonus->bonus_status : 'N/A',
            'received_bonus_on'   => $bonus ? $bonus->received_bonus_on : null,
        ]);
    }

    public function applyBonusPoints($request)
    {
        // Log::info($request);
        // $bonus = BonusEntries::where('shopify_customer_id', $request->customer_id)->first();
        // echo "<pre>"; print_r($bonus); echo "</pre>"; exit;
        // Log::info($bonus);

        // Check if current and apply_on month are same or different
        $currentMonthYear = date('F Y'); // e.g., "May 2025"
        $currentmonth = $request->apply_on === $currentMonthYear;
        Log::info("Current month status : " . $currentmonth);

        if ($currentmonth) {
            $bonus_status = "Used";
            $this->incrementCustomerTotalEntries($request->customer_id, $request->bonus_points);
        } else {
            $bonus_status = "Added";
        }

        Log::info($bonus_status);

        $updated = BonusEntries::updateOrCreate(
            [
                'shopify_customer_id' => $request->customer_id,
                'bonus_status' => 'Added', // Only target the "Added" row
            ],
            [
                'bonus_applicable_on' => $request->apply_on,
                'bonus_status' => $bonus_status, // Ensure status remains consistent
            ]
        );

        if ($updated) {
            return response()->json([
                'success' => true,
                'message' => 'Bonus applied on ' . $request->apply_on . ' successfully.',
            ], 200);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Bonus entry not found.',
            ], 404);
        }
    }

    private function incrementCustomerTotalEntries($shopifyCustomerId, $incrementBy)
    {

        Log::info($shopifyCustomerId . " : " . $incrementBy);

        Log::info('Update metafield values for total entries');
        // Your Shopify store credentials
        // First, get existing metafield value
        $metafieldResponse = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->accessToken,
        ])->get("https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/{$shopifyCustomerId}/metafields.json");

        $metafields = $metafieldResponse->json('metafields');

        // Find the 'total_entries' metafield
        $totalEntriesMetafield = collect($metafields)->firstWhere('key', 'total_entries');
        Log::info("Current Entries : ");
        Log::info($totalEntriesMetafield);

        if ($totalEntriesMetafield) {
            $currentValue = (int) $totalEntriesMetafield['value'];

            Log::info($incrementBy);

            $newValue = $currentValue + $incrementBy;
            Log::info('New Value : ' . $newValue);

            $metafields = [
                [
                    'namespace' => 'custom',
                    'key' => 'total_entries',
                    'value' => $newValue,
                    'type' => 'number_integer',
                ]
            ];

            $metafieldsUrl = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/customers/{$shopifyCustomerId}/metafields.json";

            foreach ($metafields as $metafieldData) {
                $payload = ['metafield' => $metafieldData];
                $response = Http::withHeaders([
                    'X-Shopify-Access-Token' => $this->accessToken,
                    'Content-Type' => 'application/json',
                ])->post($metafieldsUrl, $payload);

                Log::info('Metafield Updated Response', [
                    'payload' => $payload,
                    'response' => $response->body(),
                ]);
            }
            return $response->successful();
        }
        return false; // Metafield not found
    }



    public function getShopifyOrderStatus($orderId)
    {
        try {
            $order_url = "orders/{$orderId}.json";
            $response = $this->client->get($order_url);
            $order_response = json_decode($response->getBody()->getContents(), true);
            $order = $order_response['order'];

            if ($order_response) {
                //$order = $order_response->json('order');

                // Get financial and fulfillment statuses
                $financialStatus = $order['financial_status'];
                $fulfillmentStatus = $order['fulfillment_status'] ?? 'pending';

                // Get the most recent transaction status if available
                $latestTransaction = $order['transactions'] ?? [];

                $latestStatus = [
                    'order_id' => $order['id'],
                    'confirmed' => $order['confirmed'] ?? "Not confirmed",
                    'financial_status' => $financialStatus,
                    'fulfillment_status' => $fulfillmentStatus,
                    'last_transaction_status' => $latestTransaction['status'] ?? 'N/A',
                    'updated_at' => $order['updated_at'],
                    'created_at' => $order['created_at'],
                ];
                echo "<pre>";
                print_r($latestStatus);
                echo "</pre>";
                exit;
            }
        } catch (\Exception $e) {
            echo 'Unable to fetch order details.';
        }
    }

    public function getShopifyOrderTracking($orderId)
    {
        try {
            $order_url = "orders/{$orderId}.json";
            $response = $this->client->get($order_url);
            $order_response = json_decode($response->getBody()->getContents(), true);
            $order = $order_response['order'];

            if ($order_response) {
                // $order = $order_response->json('order');

                // Get financial and fulfillment statuses
                $financialStatus = $order['financial_status'];
                $fulfillmentStatus = $order['fulfillment_status'] ?? 'pending';

                $fulfillments = $order['fulfillments'] ?? [];

                $trackingInfo = [];
                if ($fulfillments) {
                    foreach ($fulfillments as $fulfillment) {
                        $trackingInfo[] = [
                            'tracking_number' => $fulfillment['tracking_number'],
                            'tracking_url' => $fulfillment['tracking_url'],
                            'carrier' => $fulfillment['tracking_company'],
                            'status' => $fulfillment['status'],
                        ];
                    }
                }

                // Get the most recent transaction status if available
                $latestTransaction = $order['transactions'] ?? [];

                $latestStatus = [
                    'order_id' => $order['id'],
                    'confirmed' => $order['confirmed'] ?? "Not confirmed",
                    'financial_status' => $financialStatus,
                    'fulfillment_status' => $fulfillmentStatus,
                    'fulfillments' => $trackingInfo,
                    'last_transaction_status' => $latestTransaction['status'] ?? 'N/A',
                    'updated_at' => $order['updated_at'],
                    'created_at' => $order['created_at'],
                ];

                $fulfillments = $order['fulfillments'];

                // echo "<pre>"; print_r($latestStatus); echo "</pre>"; exit;
            }
        } catch (\Exception $e) {
            echo 'Unable to fetch order details.';
        }
    }

    public function cancelOrder($orderId)
    {
        $response = $this->client->post("orders/{$orderId}/cancel.json");
        $responseBody = json_decode($response->getBody()->getContents(), true);
        $response_array = $this->flattenArray($responseBody);
        return $response_array['notice'];
        exit;
    }

    public function cancelRefundOrder($orderId)
    {
        $response_order = $this->client->post("orders/{$orderId}/cancel.json");
        $responseBody = json_decode($response_order->getBody()->getContents(), true);

        // echo "<pre>"; print_r($responseBody); echo "</pre>"; exit;
        // echo "<pre>" . $responseBody['payment_gateway_names'] . "</pre>"; exit;

        // Flatten the Shopify order array
        $response_array = $this->flattenArray($responseBody);

        // Display the flattened array
        echo "<pre>";
        print_r($response_array);
        echo "</pre>"; // exit;

        $simplified_array = [
            'payment_gateway' => $response_array['order_payment_gateway_names_0'],
            'subtotal_price' => $response_array['order_current_subtotal_price'],
            'total_price' => $response_array['order_current_total_price'],
            'total_tax' => $response_array['order_total_tax'],
            'total_shipping_price' => $response_array['order_total_shipping_price_set_shop_money_amount'],
            'currency' => $response_array['order_currency'],
            'total_tip_received' => $response_array['order_total_tip_received']
        ];

        // echo "<pre>"; print_r($simplified_array); echo "</pre>"; exit;

        $refundPayload =
            [
                'refund' => [
                    'note' => 'Customer requested refund',
                    'notify' => 'Test notify...',
                    'shipping' => [
                        'full_refund' => true
                    ],
                    'transactions' => [
                        'kind' => "refund",
                        'gateway' => "bogus",
                        'amount' => $response_array['order_current_total_price'],
                    ],
                    'refund_line_items' => [
                        [
                            'line_item_id' => $response_array['order_line_items_0_id'],
                            'quantity' => 1,
                            'restock_type' => 'no_restock',
                            "amount" => $response_array['order_current_total_price'],
                        ]
                    ]
                ]
            ];

        $response_refund = $this->client->post("orders/{$orderId}/refunds.json", $refundPayload);
        echo "<pre>";
        print_r($response_refund);
        echo "</pre>";
        exit;
    }

    public function calculateRefundOrder($orderId)
    {
        $response_order = $this->client->post("orders/{$orderId}/cancel.json");
        $responseBody = json_decode($response_order->getBody()->getContents(), true);

        // Flatten the Shopify order array
        $response_array = $this->flattenArray($responseBody);
        // echo "<pre>"; print_r($response_array); echo "</pre>"; exit;
        // // Display the flattened array
        // $simplified_array = [
        //     'payment_gateway' => $response_array['order_payment_gateway_names_0'],
        //     'subtotal_price' => $response_array['order_current_subtotal_price'],
        //     'total_price' => $response_array['order_current_total_price'],
        //     'total_tax' => $response_array['order_total_tax'],
        //     'total_shipping_price' => $response_array['order_total_shipping_price_set_shop_money_amount'],
        //     'currency' => $response_array['order_currency'],
        //     'total_tip_received' => $response_array['order_total_tip_received']
        // ];

        // echo "<pre>"; print_r($simplified_array); echo "</pre>"; exit;

        $refundPayload =
            [
                'refund' => [
                    'shipping' => [
                        'full_refund' => true
                    ],
                    'refund_line_items' => [
                        [
                            'line_item_id' => $response_array['order_line_items_0_id'],
                            'quantity' => 1,
                            'restock_type' => 'no_restock',
                        ]
                    ],
                    'currency' => 'USD',
                    'note' => 'Refunding for returned item.'
                ]
            ];

        $response_refund = $this->client->post("orders/{$orderId}/refunds/calculate.json", $refundPayload);
        echo "<pre>";
        print_r($response_refund);
        echo "</pre>";
        exit;
    }

    // Function to flatten a multi-dimensional array
    function flattenArray(array $array, $prefix = '')
    {
        $result = [];

        foreach ($array as $key => $value) {
            $new_key = $prefix ? "{$prefix}_{$key}" : $key;

            if (is_array($value)) {
                $result = array_merge($result, $this->flattenArray($value, $new_key));
            } else {
                $result[$new_key] = $value;
            }
        }

        return $result;
    }

    // Fetch brand wise products
    public function fetchProductsByVendor($vendor)
    {
        $query = [
            'vendor' => $vendor,
            'limit' => 50,
        ];
        $response = $this->client->get("products.json", [
            'query' => $query
        ]);

        // echo "<pre>"; print_r($response); echo "</pre>"; exit;
        $products = json_decode($response->getBody()->getContents(), true)['products'];
        echo "<pre>";
        print_r($products);
        echo "</pre>";
        exit;
    }

    // Fetch brand wise products with graphql
    public function getFilteredProducts(Request $request)
    {
        // echo $request->input('vendor'); exit;

        // GraphQL endpoint
        $url = "https://{$this->storeName}.myshopify.com/admin/api/{$this->apiVersion}/graphql.json";

        // Get filter parameters from request
        $vendor = $request->input('vendor');
        // $productType = $request->input('productType');
        // $tags = $request->input('tags');  // Example: "Tag1,Tag2"
        // $first = $request->input('first', 10);  // Default to 10 products
        $title = $request->input('title');  // Partial title search

        $minPrice = $request->input('min_price');  // Minimum price filter
        $maxPrice = $request->input('max_price');
        $availableOnly = $request->input('available_only', false);

        $first = 10;

        // Build query string based on provided filters
        $queryFilters = [];
        if ($vendor) {
            $queryFilters[] = "vendor:'$vendor'";
        }
        if ($title) {
            // Search for products with the title containing the specified text (case-insensitive)
            $queryFilters[] = "title:*$title*";
        }
        if ($minPrice && $maxPrice) {
            $queryFilters[] = "variant.price:>=$minPrice AND variant.price:<=$maxPrice";
        } elseif ($minPrice) {
            $queryFilters[] = "variant.price:>=$minPrice";
        } elseif ($maxPrice) {
            $queryFilters[] = "variant.price:<=$maxPrice";
        }
        if ($availableOnly) {
            $queryFilters[] = "available_for_sale:true";
        }

        // Join the filters into a single query string
        $queryString = implode(' AND ', $queryFilters);

        // Define the GraphQL query using the filters
        $query = <<<GRAPHQL
        query getFilteredProducts(\$query: String!, \$first: Int!) {
          products(first: \$first, query: \$query) {
            edges {
              node {
                id
                title
                vendor
                productType
                tags
                descriptionHtml
                collections(first: 5) {
                  edges {
                    node {
                      id
                      title
                    }
                  }
                }
                options {
                  id
                  name
                  values
                }
                variants(first: 5) {
                  edges {
                    node {
                      id
                      title
                      sku
                      price
                    }
                  }
                }
                images(first: 3) {
                  edges {
                    node {
                      id
                      src
                      altText
                    }
                  }
                }
              }
            }
          }
        }
        GRAPHQL;

        $response = $this->client->post($url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Shopify-Access-Token' => $this->accessToken,
            ],
            'json' => [
                'query' => $query,
                'variables' => [
                    'query' => $queryString,
                    'first' => $first,
                ],
            ],
        ]);

        // Handle the response
        if ($response->getStatusCode() == 200) {
            $data = json_decode($response->getBody(), true);
            // $collections = $data['data']['product']['collections']['edges'];
            $products = $data['data']['products'];
            // echo "<pre>"; print_r($products); exit;
            // return $product_data;

            $transformedProducts = [];

            if (isset($products['edges'])) {
                foreach ($products['edges'] as $edge) {
                    $transformedProducts[] = $edge['node'];
                }
                echo "<pre>";
                print_r($transformedProducts);
                echo "</pre>";
                exit;

                // Convert and output JSON
                $json_data = $this->convertToJSON($transformedProducts);
                // echo $json_data; exit;

                $json_array = array('products' => $json_data);
                $products_array = json_encode($json_array);
                echo "<pre>";
                print_r($products_array);
                echo "</pre>";
                exit;
            } else {
                return NULL;
            }
            // echo "<pre>"; print_r($transformedCollections); exit;
            //return view('product_collections', compact('product_data'));

        } else {
            $error = "GraphQL query failed: " . $response->getBody();
            return view('error', compact('error'));
        }
    }

    public function createCustomerAccessToken(Request $request)
    {
        $email = $request->input('email');
        $password = $request->input('password');

        $query = <<<GQL
        mutation customerAccessTokenCreate(\$input: CustomerAccessTokenCreateInput!) {
            customerAccessTokenCreate(input: \$input) {
                customerAccessToken {
                    accessToken
                    expiresAt
                }
                userErrors {
                    field
                    message
                }
            }
        }
        GQL;

        $variables = [
            'input' => [
                'email' => $email,
                'password' => $password,
            ],
        ];

        $response = Http::withHeaders([
            'X-Shopify-Storefront-Access-Token' => $this->accessTokenFront,
            'Content-Type' => 'application/json',
        ])->post("https://{$this->storeName}.myshopify.com/api/{$this->apiVersion}/graphql.json", [
            'query' => $query,
            'variables' => $variables,
        ]);

        if ($response->successful()) {
            return $response->json('data.customerAccessTokenCreate');
        }

        return ['error' => $response->json('errors')];
    }

    // ============= Start - Stripe Functions ==============
    public function findCustomerByEmail()
    {
        $email = '<EMAIL>';
        Stripe::setApiKey(env('STRIPE_SECRET'));

        try {
            // List customers, filtering by email (server-side filtering)
            $customers = StripeCustomer::all(['limit' => 100]); // adjust limit as needed

            Log::info($customers);

            foreach ($customers->data as $customer) {
                if ($customer->email === $email) {
                    return response()->json([
                        'success' => true,
                        'customer' => $customer
                    ]);
                }
            }

            return response()->json([
                'success' => false,
                'message' => 'Customer not found'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function createCustomerByEmail($shopifyCustomerId, $email, $name)
    {
        Log::info("Stripe customer creating by Email...");

        Stripe::setApiKey(env('STRIPE_SECRET'));

        // Check if user already has a Stripe customer ID
        $customer  = Customer::where('shopify_customer_id', $shopifyCustomerId)->get()->toArray();
        // echo "<pre>"; print_r($customer); echo "</pre>"; exit;

        if ($customer[0]['payment_method_id'] == '' && $customer[0]['payment_method_url'] == "") {
            Log::info("Payment method is not set");

            $stripeCustomer = StripeCustomer::create([
                'email' => $email,
                'name' => $name,
            ]);

            Log::info('Stripe Customer');
            Log::info($stripeCustomer);

            // Generate Stripe Setup URL :
            $this->createPaymentMethod($shopifyCustomerId, $stripeCustomer->id, $email);

            //return $customer;
        }
        // elseif($customer[0]['payment_method_id']=='' && $customer[0]['payment_method_url']!=""){
        //     $this->getPaymentMethod($shopifyCustomerId, $customer[0]['stripe_customer_id'], $email);
        // }

    }

    public function createPaymentMethod($shopifyCustomerId, $stripeCustomerId, $email)
    {
        Log::info("Customer detail for stripe : " . $shopifyCustomerId . " : " . $stripeCustomerId);
        // Create a Checkout session for card setup
        $session = Session::create([
            'customer' => $stripeCustomerId,
            'payment_method_types' => ['card'],
            'mode' => 'setup', // We're only collecting card for future use
            'success_url' => 'https://noblerewardsclub.myshopify.com/',
            'cancel_url' => 'https://noblerewardsclub.myshopify.com/',
        ]);
        Log::info("Stripe URL :");
        Log::info($session->url);

        // Get email based on Shopify customer ID
        // $email = $this->getCustomerEmailByShopifyId($shopifyCustomerId); // implement this method
        Log::info("Customer Email : " . $email);

        Customer::updateOrCreate(
            ['shopify_customer_id' => $shopifyCustomerId],
            [
                'stripe_customer_id' => $stripeCustomerId,
                'payment_method_url' => $session->url != '' ? $session->url : "",
            ]
        );

        // Send email to customer
        if ($email) {
            Mail::to($email)->send(new SetupPaymentLinkMail($session->url));
            Log::info("Setup link emailed to customer: " . $email);
        } else {
            Log::warning("Email not found for Shopify customer ID: " . $shopifyCustomerId);
        }
        // return response()->json(['url' => $session->url]);
    }
    public function createCardUpdateSession($stripeCustomerId, $email)
    {
        try {
            Log::info("Creating card update session for Stripe customer: " . $stripeCustomerId);

            Stripe::setApiKey(env('STRIPE_SECRET'));

            // Create a Checkout session for card update
            $session = Session::create([
                'customer' => $stripeCustomerId,
                'payment_method_types' => ['card'],
                'mode' => 'setup', // We're only collecting card for future use
                'success_url' => 'https://noblerewardsclub.myshopify.com/account?card_updated=success',
                'cancel_url' => 'https://noblerewardsclub.myshopify.com/account?card_updated=cancelled',
            ]);

            Log::info("Card update URL generated: " . $session->url);

            return $session->url;
        } catch (\Exception $e) {
            Log::error("Error creating card update session: " . $e->getMessage());
            return null;
        }
    }
    protected function getCustomerEmailByShopifyId($shopifyCustomerId)
    {
        // Assuming you store Shopify customers in your DB
        $customer = Customer::where('shopify_customer_id', $shopifyCustomerId)->first();
        return $customer?->email;
    }

    public function getPaymentMethod($shopifyCustomerId, $stripeCustomerId, $email)
    {
        // $email = '<EMAIL>';
        // $name = 'Guest User5';
        // $customerId = "cus_SMe1BLPJkKETl4";

        Stripe::setApiKey(env('STRIPE_SECRET'));

        $paymentMethodResponse = PaymentMethod::all([
            'customer' => $stripeCustomerId,
            'type' => 'card',
        ]);
        Log::info("Payment Method Response : ");
        Log::info($paymentMethodResponse);

        $paymentMethods = array_map(function ($pm) {
            return $pm->toArray();
        }, $paymentMethodResponse->data);

        Log::info('Payment Methods Array : ');
        Log::info($paymentMethods);

        // Get the first saved card
        if ($paymentMethods) {

            $paymentMethodId = $paymentMethods[0]['id'];

            Log::info("Payment Method ID: " . $paymentMethodId);

            Customer::updateOrCreate(
                ['shopify_customer_id' => $shopifyCustomerId],
                [
                    'payment_method_id' => $paymentMethodId,
                    'payment_method_status' => 'Active'
                ]
            );
            Log::info('Payment method received');
            return "Payment method received";
        } else {
            Log::info('Payment method not created');
            return "Payment method not created";
        }
    }

    public function makePayment($customerId, $amount, $payment_method)
    {
        // $email = '<EMAIL>';
        // $name = 'Guest User5';
        // $customerId = "cus_SMe1BLPJkKETl4";
        // echo "Upcoming payment to stripe : ";
        // echo $customerId . " : " . $amount . " : " . $payment_method;

        $amount_payble = number_format($amount, 2) * 100;

        Stripe::setApiKey(env('STRIPE_SECRET'));

        $paymentIntent = \Stripe\PaymentIntent::create([
            'amount' => $amount_payble, // in cents
            'currency' => 'usd',
            'customer' => $customerId,
            'payment_method' => $payment_method,
            'off_session' => true,
            'confirm' => true,
        ]);
        Log::info("Payment Info : ");
        $paymentIntentArray = $paymentIntent->toArray();
        Log::info($paymentIntentArray);
        return $paymentIntentArray;
        //$chargeId = $paymentIntent->charges->data[0]->id;
        //Log::info("Charge ID: " . $chargeId);
    }
    // ============= End - Stripe Functions ==============
}
